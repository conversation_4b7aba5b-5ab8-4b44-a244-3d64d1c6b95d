import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import { ErrorLabel, FormButton, MockButton, TableFormInnerBox } from '@src/components/FormComponents';
import { DEBUG } from '@src/constants';
import type { FormConfig, Maybe } from '@src/typesGlobal';
import { zodErrorMap } from '@src/utils/utils';
import { useRef } from 'react';
import { mockMessages } from './gcrFltMockData';
import type { GcrFltMessage } from './gcrFltTypes';
import { gcrFltMessageSchema } from './gcrFltValidation';
import { useGcrFltStore } from './useGcrFltStore';

const showError = (error: Maybe<string>) => <ErrorLabel error={error} />;

type ScrMessageFormProps = {
  formConfig: FormConfig<GcrFltMessage>;
  getOnChange: (key: keyof GcrFltMessage) => (e: React.ChangeEvent<HTMLInputElement>) => void;
};

export const GcrFltMessageFormTableRenderer: React.FC<ScrMessageFormProps> = (props) => {
  const { formConfig, getOnChange } = props;
  const { inputs } = formConfig;
  const {
    setMessage,
    formMessage,
    setFormMessagePartial,
    clearFormMessage,
    formMessageErrors: errors,
    setFormMessageErrors,
    clearFormMessageErrors,
  } = useGcrFltStore();

  const formRef = useRef<HTMLDivElement>(null);

  const handleAddMessage = () => {
    const parsed = gcrFltMessageSchema.safeParse(formMessage);
    if (!parsed.success) {
      const errorMap = zodErrorMap(parsed.error);
      // console.log('errorMap', errorMap);
      setFormMessageErrors(errorMap);
      return;
    }
    clearFormMessageErrors();
    setMessage(parsed.data);
  };

  return (
    <div ref={formRef}>
      <Table /* component={Paper} */ size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <colgroup>
          <col style={{ width: '20%' }} />
          <col style={{ width: '20%' }} />
          <col style={{ width: '15%' }} />
          <col style={{ width: '45%' }} />
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell>{formConfig.inputs.season.label}</TableCell>
            <TableCell>{formConfig.inputs.date.label}</TableCell>
            <TableCell>{formConfig.inputs.airport.label}</TableCell>
            <TableCell>{formConfig.inputs.creator.label}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell>
              <TextField
                value={formMessage?.season || ''}
                onChange={getOnChange(inputs.season.storeKey)}
                error={!!errors?.season}
                helperText={showError(errors?.season)}
              />
            </TableCell>
            <TableCell>
              <TextField
                value={formMessage?.date || ''}
                onChange={getOnChange(inputs.date.storeKey)}
                error={!!errors?.date}
                helperText={showError(errors?.date)}
              />
            </TableCell>
            <TableCell>
              <TextField
                value={formMessage?.airport || ''}
                onChange={getOnChange(inputs.airport.storeKey)}
                error={!!errors?.airport}
                helperText={showError(errors?.airport)}
              />
            </TableCell>
            <TableCell>
              <TextField
                onBlur={(e) => {
                  setFormMessagePartial({ creator: e.target.value });
                }}
                error={!!errors?.creator}
                helperText={showError(errors?.creator)}
              />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell component="th" scope="row">
              Scheduled Information:
            </TableCell>
            <TableCell colSpan={3}>
              <TextField
                multiline
                minRows={2}
                maxRows={2}
                // value={formMessage?.si || ''}
                // onBlur={getOnChange(inputs.si)}
                onBlur={(e) => {
                  setFormMessagePartial({ si: e.target.value });
                }}
                // onChange={getOnChange(inputs.si)}
                error={!!errors?.si}
                helperText={showError(errors?.si)}
              />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell component="th" scope="row">
              General Information:
            </TableCell>
            <TableCell colSpan={3}>
              <TextField
                multiline
                minRows={2}
                maxRows={2}
                // value={formMessage?.gi || ''}
                // onChange={getOnChange(inputs.gi)}
                onBlur={(e) => {
                  setFormMessagePartial({ gi: e.target.value });
                }}
                error={!!errors?.gi}
                helperText={showError(errors?.gi)}
              />
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      <TableFormInnerBox>
        {DEBUG &&
          mockMessages.map((msg, i) => (
            <MockButton key={i} onClick={() => setFormMessagePartial(msg)}>
              {i}
            </MockButton>
          ))}
      </TableFormInnerBox>
      <TableFormInnerBox>
        <FormButton onClick={handleAddMessage}>Add Message</FormButton>
        <FormButton
          variant="contained"
          color="warning"
          onClick={() => {
            clearFormMessage();
            // Reset uncontrolled inputs:
            const inputs = formRef.current?.querySelectorAll('input, textarea') || [];
            inputs.forEach((input) => ((input as HTMLInputElement).value = ''));
            clearFormMessageErrors();
          }}
        >
          Clear
        </FormButton>
      </TableFormInnerBox>
    </div>
  );
};
