import MenuItem from '@mui/material/MenuItem';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import { ErrorLabel, FormButton, MockButton, TableFormInnerBox } from '@src/components/FormComponents';
import { DEBUG } from '@src/constants';
import type { FormConfig, Maybe } from '@src/typesGlobal';
import { mockLines } from './gcrRegMockData';
import { useGcrRegStore } from './gcrRegStore';
import type { GcrRegLine } from './gcrRegTypes';

const showError = (error: Maybe<string>) => <ErrorLabel error={error} />;

type ScrLineFormProps = {
  formConfig: FormConfig<GcrRegLine>;
  handleAddLine: () => void;
  getOnChange: (key: keyof GcrRegLine) => (e: React.ChangeEvent<HTMLInputElement>) => void;
};

export const GcrRegLineFormTableRenderer: React.FC<ScrLineFormProps> = (props) => {
  const { formConfig, getOnChange, handleAddLine } = props;
  const { inputs } = formConfig;

  const { formLine, formLineIndex, clearFormLine, formLineErrors: errors, setFormLinePartial } = useGcrRegStore();
  // console.log(`errors`, errors);

  const isEdit = formLineIndex !== null;

  return (
    <>
      <Table size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <colgroup>
          <col style={{ width: '2%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          {/* 5 */}
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          {/* 10 */}
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          {/* <col style={{ width: '3%' }} /> */}
          {/* <col style={{ width: '4%' }} /> */}
          {/* 15 */}
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell></TableCell>
            <TableCell align="center">C/R/N/D</TableCell>
            <TableCell align="center">Acreg</TableCell>
            <TableCell>Date</TableCell>
            <TableCell align="center">Seats</TableCell>
            {/* 5 */}
            <TableCell align="center">A/C</TableCell>
            <TableCell align="center">Origin</TableCell>
            <TableCell align="center">Prev</TableCell>
            <TableCell align="center">Time</TableCell>
            <TableCell align="center">O/N</TableCell>
            {/* 10 */}
            <TableCell align="center">Next</TableCell>
            <TableCell align="center">Dest</TableCell>
            <TableCell align="center">St</TableCell>
            {/* 13 */}
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell component="th" scope="row">
              Arr
            </TableCell>
            {/* Action */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs.action.label}
                name={inputs.action.storeKey}
                select
                value={formLine?.action || ''}
                onChange={getOnChange(inputs.action.storeKey)}
                error={!!errors?.[inputs.action.storeKey]}
                helperText={showError(errors?.[inputs.action.storeKey])}
              >
                {inputs.action.options?.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </TableCell>
            {/* Aircraft Registration */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.aircraftRegistration?.label}
                name={inputs?.aircraftRegistration?.storeKey}
                value={formLine?.aircraftRegistration || ''}
                onChange={getOnChange(inputs?.aircraftRegistration.storeKey)}
                error={!!errors?.[inputs.aircraftRegistration.storeKey]}
                helperText={showError(errors?.[inputs.aircraftRegistration.storeKey])}
              />
            </TableCell>
            {/* Date  */}
            <TableCell rowSpan={2}>
              {/* <Box sx={{ display: 'flex', gap: 1 }}> */}
              <TextField
                label={inputs?.date?.label}
                name={inputs?.date?.storeKey}
                value={formLine?.date || ''}
                onChange={getOnChange(inputs?.date.storeKey)}
                error={!!errors?.[inputs?.date.storeKey]}
                helperText={showError(errors?.[inputs?.date.storeKey])}
                sx={{ maxWidth: '5rem' }}
              />
              {/* </Box> */}
            </TableCell>
            {/* Seats */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.seats?.label}
                name={inputs?.seats?.storeKey}
                value={formLine?.seats || ''}
                onChange={getOnChange(inputs?.seats.storeKey)}
                error={!!errors?.[inputs.seats.storeKey]}
                helperText={showError(errors?.[inputs.seats.storeKey])}
              />
            </TableCell>
            {/* Aircraft Type */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.aircraftType?.label}
                name={inputs?.aircraftType?.storeKey}
                value={formLine?.aircraftType || ''}
                onChange={getOnChange(inputs?.aircraftType.storeKey)}
                error={!!errors?.[inputs.aircraftType.storeKey]}
                helperText={showError(errors?.[inputs.aircraftType.storeKey])}
              />
            </TableCell>
            {/* Origin */}
            <TableCell>
              <TextField
                label={inputs?.origin?.label}
                name={inputs?.origin?.storeKey}
                value={formLine?.origin || ''}
                onChange={getOnChange(inputs?.origin.storeKey)}
                error={!!errors?.[inputs.origin.storeKey]}
                helperText={showError(errors?.[inputs.origin.storeKey])}
              />
            </TableCell>
            {/* Previous */}
            <TableCell>
              <TextField
                label={inputs?.previous?.label}
                name={inputs?.previous?.storeKey}
                value={formLine?.previous || ''}
                onChange={getOnChange(inputs?.previous.storeKey)}
                error={!!errors?.[inputs.previous.storeKey]}
                helperText={showError(errors?.[inputs.previous.storeKey])}
              />
            </TableCell>
            {/* Time 1 */}
            <TableCell>
              <TextField
                label={inputs?.timeArr?.label}
                name={inputs?.timeArr?.storeKey}
                value={formLine?.timeArr || ''}
                onChange={getOnChange(inputs?.timeArr.storeKey)}
                error={!!errors?.[inputs?.timeArr.storeKey]}
                helperText={showError(errors?.[inputs?.timeArr.storeKey])}
              />
            </TableCell>
            {/* O/N */}
            <TableCell>{/* O/N */}</TableCell>
            {/* Next */}
            <TableCell>{/* Next */}</TableCell>
            {/* Destination */}
            <TableCell>{/* Dest */}</TableCell>
            {/* Service Type*/}
            <TableCell>
              <TextField
                label={inputs?.stArr?.label}
                name={inputs?.stArr?.storeKey}
                value={formLine?.stArr || ''}
                onChange={getOnChange(inputs?.stArr.storeKey)}
                select
                error={!!errors?.[inputs.stArr.storeKey]}
                helperText={showError(errors?.[inputs.stArr.storeKey])}
              >
                {inputs?.stArr?.options?.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell component="th" scope="row">
              Dep
            </TableCell>
            {/* Action */}
            {/* rowSpan={2} */}
            {/* Date Range */}
            {/* rowSpan={2} */}
            {/* DOOP */}
            {/* rowSpan={2} */}
            {/* Seats */}
            {/* rowSpan={2} */}
            {/* Aircraft Type */}
            {/* rowSpan={2} */}
            {/* Origin */}
            <TableCell>{/* Origin */}</TableCell>
            {/* Previous */}
            <TableCell>{/* Prev */}</TableCell>
            {/* Time 2 */}
            <TableCell>
              <TextField
                label={inputs?.timeDep?.label}
                name={inputs?.timeDep?.storeKey}
                value={formLine?.timeDep || ''}
                onChange={getOnChange(inputs?.timeDep.storeKey)}
                error={!!errors?.[inputs?.timeDep.storeKey]}
                helperText={showError(errors?.[inputs?.timeDep.storeKey])}
              />
            </TableCell>
            {/* O/N */}
            <TableCell>
              {/* O/N */}
              <TextField
                label={inputs?.on?.label}
                name={inputs?.on?.storeKey}
                value={formLine?.on || ''}
                onChange={getOnChange(inputs?.on.storeKey)}
                error={!!errors?.[inputs.on.storeKey]}
                helperText={showError(errors?.[inputs.on.storeKey])}
              />
            </TableCell>
            {/* Next */}
            <TableCell>
              {/* Next */}
              <TextField
                label={inputs?.next?.label}
                name={inputs?.next?.storeKey}
                value={formLine?.next || ''}
                onChange={getOnChange(inputs?.next.storeKey)}
                error={!!errors?.[inputs.next.storeKey]}
                helperText={showError(errors?.[inputs.next.storeKey])}
              />
            </TableCell>
            {/* Destination */}
            <TableCell>
              {/* Dest */}
              <TextField
                label={inputs?.destination?.label}
                name={inputs?.destination?.storeKey}
                value={formLine?.destination || ''}
                onChange={getOnChange(inputs?.destination.storeKey)}
                error={!!errors?.[inputs.destination.storeKey]}
                helperText={showError(errors?.[inputs.destination.storeKey])}
              />
            </TableCell>
            {/* Service Type*/}
            <TableCell>
              <TextField
                label={inputs?.stDep?.label}
                name={inputs?.stDep?.storeKey}
                value={formLine?.stDep || ''}
                onChange={getOnChange(inputs?.stDep.storeKey)}
                select
                error={!!errors?.[inputs?.stDep.storeKey]}
                helperText={showError(errors?.[inputs?.stDep.storeKey])}
              >
                {inputs?.stDep?.options?.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </TableCell>
            {/* Frequency */}
            {/* rowSpan={2} */}
            {/* Aircraft Registration */}
            {/* rowSpan={2} */}
          </TableRow>
        </TableBody>
      </Table>

      {DEBUG && (
        <TableFormInnerBox>
          {mockLines.map((line, i) => (
            <MockButton key={i} onClick={() => setFormLinePartial({ ...line })}>
              {i}
            </MockButton>
          ))}
        </TableFormInnerBox>
      )}

      <TableFormInnerBox>
        <FormButton variant="contained" size="small" onClick={handleAddLine}>
          {isEdit ? `Update Line` : `Add Line`}
        </FormButton>
        <FormButton variant="contained" color="warning" size="small" onClick={() => clearFormLine()}>
          Clear
        </FormButton>
      </TableFormInnerBox>
    </>
  );
};
