import type { FormConfig } from '@src/typesGlobal';
import type { GcrFltLine } from './gcrFltTypes';
import { lineActionValues, serviceTypeValues } from '@src/validation/validationSchemas';

export const gcrFltLineFormConfig: FormConfig<GcrFltLine> = {
  inputs: {
    action: {
      storeKey: 'action',
      label: 'Action',
      type: 'select',
      options: lineActionValues,
    },
    operatorArr: {
      storeKey: 'operatorArr',
      label: '',
      type: 'text',
      toUpperCase: true,
    },
    flightNumberArr: {
      storeKey: 'flightNumberArr',
      label: '',
      type: 'text',
      toUpperCase: true,
    },
    operatorDep: {
      storeKey: 'operatorDep',
      label: '',
      type: 'text',
      toUpperCase: true,
    },
    flightNumberDep: {
      storeKey: 'flightNumberDep',
      label: '',
      type: 'text',
      toUpperCase: true,
    },
    date: {
      storeKey: 'date',
      label: '',
      type: 'text',
      toUpperCase: true,
    },
    seats: {
      storeKey: 'seats',
      label: '',
      type: 'number',
    },
    aircraftType: {
      storeKey: 'aircraftType',
      label: '',
      type: 'text',
    },
    origin: {
      storeKey: 'origin',
      label: '',
      type: 'text',
      toUpperCase: true,
    },
    previous: {
      storeKey: 'previous',
      label: '',
      type: 'text',
      toUpperCase: true,
    },
    timeArr: {
      storeKey: 'timeArr',
      label: '',
      type: 'text',
    },
    timeDep: {
      storeKey: 'timeDep',
      label: '',
      type: 'text',
    },
    on: {
      storeKey: 'on',
      label: '',
      type: 'number',
    },
    next: {
      storeKey: 'next',
      label: '',
      type: 'text',
      toUpperCase: true,
    },
    destination: {
      storeKey: 'destination',
      label: '',
      type: 'text',
      toUpperCase: true,
    },
    stArr: {
      storeKey: 'stArr',
      label: '',
      type: 'select',
      options: serviceTypeValues,
    },
    stDep: {
      storeKey: 'stDep',
      label: '',
      type: 'select',
      options: serviceTypeValues,
    },
    aircraftRegistration: {
      storeKey: 'aircraftRegistration',
      type: 'text',
      label: '',
      toUpperCase: true,
    },
  },
};
