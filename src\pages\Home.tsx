import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { appPages } from '@src/constants';
import { Link } from 'react-router-dom';

export const Home: React.FC = () => {
  return (
    <Paper elevation={3} sx={{ padding: '2rem', maxWidth: '25rem', mx: 'auto', mt: '4rem' }}>
      <Typography variant="h2" gutterBottom sx={{ color: 'primary.main' }}>
        Aviation Messages
      </Typography>
      <Typography variant="h5" sx={{ mt: '2rem' }}>
        Select a module to get started:
      </Typography>
      <Stack spacing={'.75rem'} sx={{ mt: '1.5rem' }}>
        {appPages
          .filter((page) => page.path !== '/')
          .map((page) => (
            <Button
              key={page.path}
              component={Link}
              to={page.path}
              variant="contained"
              fullWidth
              endIcon={<ArrowForwardIcon />}
              sx={{ py: 1.5, textTransform: 'none', m: '1rem' }}
            >
              {page.name}
            </Button>
          ))}
      </Stack>
    </Paper>
  );
};
