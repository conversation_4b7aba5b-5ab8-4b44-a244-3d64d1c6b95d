# Aviation Messages

This is a tool for creating multiple types of aviation messages.

## Prerequisites

You need the following software installed on your system:

- [Git](https://git-scm.com/) - Version control system
- [Node.js](https://nodejs.org/) - JavaScript runtime (version 18 or higher recommended)
- [PNPM](https://pnpm.io/) - Package manager

## Getting Started

### 1. <PERSON><PERSON> the Repository

```sh
git clone <repository-url>
cd diyssim
```

### 2. Install Dependencies

Install PNPM globally (if not already installed):

```sh
npm install -g pnpm
```

Install project dependencies:

```sh
pnpm install
```

### 3. Development

Start the development server:

```sh
pnpm dev
```

The application will be available at `http://localhost:5173` (or another port if 5173 is in use).

## Build Instructions

### Production Build

To create a production build:

```sh
pnpm build
```

This command:

1. Runs TypeScript compilation (`tsc -b`)
2. Builds the application using Vite (`vite build`)
3. Outputs the built files to the `dist/` directory

### Preview Production Build

To preview the production build locally:

```sh
pnpm preview
```

This serves the built application from the `dist/` directory.

## Development Commands

| Command        | Description                              |
| -------------- | ---------------------------------------- |
| `pnpm dev`     | Start development server with hot reload |
| `pnpm build`   | Create production build                  |
| `pnpm preview` | Preview production build locally         |
| `pnpm lint`    | Run ESLint to check code quality         |

## Code Quality

### Linting

Run ESLint to check for code quality issues:

```sh
pnpm lint
```

### Type Checking

TypeScript type checking is automatically performed during development and build processes via the `vite-plugin-checker` plugin.

To manually run type checking:

```sh
npx tsc --noEmit
```

## Project Structure

```
├── src/                    # Source code
│   ├── components/         # Reusable React components
│   ├── pages/             # Page components
│   ├── utils/             # Utility functions and factories
│   ├── validation/        # Form validation schemas
│   └── main.tsx           # Application entry point
├── public/                # Static assets
├── dist/                  # Production build output (generated)
├── .azure-pipelines/      # Azure DevOps CI/CD configuration
├── package.json           # Project dependencies and scripts
├── vite.config.ts         # Vite configuration
├── tsconfig.json          # TypeScript configuration
└── eslint.config.js       # ESLint configuration
```

## Technology Stack

- **Frontend Framework**: React 19 with TypeScript
- **Build Tool**: Vite 6
- **UI Library**: Material-UI (MUI) 7
- **State Management**: Zustand with Immer
- **Form Validation**: Zod
- **Routing**: React Router 7
- **Styling**: Emotion (CSS-in-JS)
- **Code Quality**: ESLint with TypeScript support

## Deployment

The project includes Azure DevOps pipeline configuration in `.azure-pipelines/` for automated deployment to Azure Web Apps.

### Manual Deployment

1. Build the project:

   ```sh
   pnpm build
   ```

2. Deploy the contents of the `dist/` directory to your web server.

## Troubleshooting

### Common Issues

**Port already in use**: If port 5173 is already in use, Vite will automatically try the next available port.

**TypeScript errors**: Make sure all dependencies are installed and TypeScript configuration is correct:

```sh
pnpm install
npx tsc --noEmit
```

**Build failures**: Ensure all linting issues are resolved before building:

```sh
pnpm lint
pnpm build
```
