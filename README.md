# Aviation Messages

This is a tool for creating aviation messages. It is currently in development and not yet ready for use.

This README is a work in progress.

## Getting Started

You need [Git](https://git-scm.com/), [Node.js](https://nodejs.org/), and the [PNPM](https://pnpm.io/) package manager installed.

1. Make sure Git and Node.js are installed on your system.
2. Clone the Git repository locally.
3. Install PNPM (if not already installed):
   ```sh
   npm install -g pnpm
   ```
4. Navigate to the project folder:  
   `(repo)/AviationMessages/`
5. Run `pnpm install` to install dependencies.
6. Start the development server with `pnpm dev`.

The app will now be running locally and can be tested in your browser.
