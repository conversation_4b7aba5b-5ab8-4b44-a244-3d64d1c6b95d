import { MenuItem, TextField } from '@mui/material';
import { ErrorLabel } from '@src/components/FormComponents';
import type { Maybe } from '@src/typesGlobal';
import { gcrFltLineFormConfig } from './gcrFltLineFormConfig';
import { getGcrFltLineOnChange, getGcrFltOnKeyHandler } from './gcrFltLineFormUtils';
import type { GcrFltLine } from './gcrFltTypes';
import { useGcrFltStore } from './useGcrFltStore';

const showError = (error: Maybe<string>) => <ErrorLabel error={error} />;

type GcrFltLineTextFieldProps = React.ComponentProps<typeof TextField> & {
  currentInputKey: keyof GcrFltLine;
};

export const GcrFltTextField: React.FC<GcrFltLineTextFieldProps> = (props) => {
  const { currentInputKey, ...rest } = props;
  const inputs = gcrFltLineFormConfig.inputs;
  const thisInput = inputs[currentInputKey];

  const {
    formLine,
    formLineErrors: errors,
    setFormLinePartial: updateLine,
    formLineFocusKey,
    setFormLineFocusKey,
  } = useGcrFltStore();
  // console.log(`errors`, errors);

  // Check if this input has options (making it a select field)
  const isSelect = !!thisInput.options && thisInput.options.length > 0;

  return (
    <TextField
      label={thisInput.label}
      name={thisInput.storeKey}
      select={isSelect}
      value={formLine?.[currentInputKey] || ''}
      onChange={getGcrFltLineOnChange(currentInputKey, updateLine)}
      onKeyDown={getGcrFltOnKeyHandler(inputs, currentInputKey, updateLine, setFormLineFocusKey)}
      focused={formLineFocusKey === currentInputKey}
      helperText={showError(errors?.[currentInputKey])}
      error={!!errors?.[currentInputKey]}
      {...rest}
    >
      {isSelect &&
        thisInput.options?.map((option) => (
          <MenuItem key={option} value={option}>
            {option}
          </MenuItem>
        ))}
    </TextField>
  );
};
