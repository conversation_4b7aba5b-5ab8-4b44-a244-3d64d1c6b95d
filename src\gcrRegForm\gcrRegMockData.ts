import type { GcrRegArrLine, GcrRegDepLine, GcrRegLineFormInput, GcrRegMessage } from './gcrRegTypes';

const dummyArr: GcrRegArrLine = {
  action: 'N',
  date: '01JUN',
  seats: 189,
  aircraftType: 'HOPP',
  origin: 'CPHG',
  previous: 'AMST',
  timeArr: '1300',
  stArr: 'K',
  aircraftRegistration: 'OY-KAL',
};

const dummyDep: GcrRegDepLine = {
  action: 'N',
  date: '01JUN',
  seats: 189,
  aircraftType: 'HOPP',
  timeDep: '1400',
  on: 0,
  next: 'FRAN',
  destination: 'LHRT',
  stDep: 'K',
  aircraftRegistration: 'OY-KAL',
};

export const mockLines: GcrRegLineFormInput[] = [
  // Arrival:
  { ...dummyArr },
  // Full of errors:
  {
    ...dummyArr,
    date: '01JUNE',
    origin: 'CP',
    previous: 'AMS',
    timeArr: '130',
    timeDep: '140',
    on: 9,
    next: 'RA',
    destination: 'LH',
  },
  // Departure:
  { ...dummyDep },
  // Change:
  {
    ...dummyArr,
    action: 'C',
    seats: 220,
    aircraftType: '320M',
  },
  // Request:
  { ...dummyArr, action: 'R' },
];

const dummyMessage: GcrRegMessage = {
  season: 'W26',
  date: '21MAY',
  airport: 'CPHG',
  creator: '<EMAIL>',
  si: 'STUFF',
  gi: 'MORE',
};

export const mockMessages: GcrRegMessage[] = [
  { ...dummyMessage },
  // Full of errors:
  {
    ...dummyMessage,
    season: 'W2',
    date: '2MAY',
    airport: 'CP',
    creator: 'your.email@example',
    si: '',
    gi: '',
  },
  // No errors:
  {
    ...dummyMessage,
    season: 'S25',
    date: '26MAY',
    airport: 'AMST',
    creator: '<EMAIL>',
    si: 'STUFF',
    gi: 'MORE',
  },
];
