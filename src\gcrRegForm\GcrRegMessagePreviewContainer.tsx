import { MessagePreview } from '@src/components/MessagePreview';
import { useMemo } from 'react';
import { useGcrRegStore } from './gcrRegStore';
import { formatGcrFltMsgHeader, formatScrMsgFooter } from './gcrRegUtils';

export const GcrRegMessagePreviewContainer: React.FC = () => {
  const { message, clearMessage } = useGcrRegStore();
  const msgHeader = useMemo(() => message && formatGcrFltMsgHeader(message), [message]);
  const msgFooter = useMemo(() => message && formatScrMsgFooter(message), [message]);

  return <MessagePreview clearMessage={clearMessage} msgHeader={msgHeader} msgFooter={msgFooter} />;
};
