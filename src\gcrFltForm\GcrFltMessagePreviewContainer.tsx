import { MessagePreview } from '@src/components/MessagePreview';
import { useMemo } from 'react';
import { formatScrMsgFooter, formatGcrFltMsgHeader } from './gcrFltUtils';
import { useGcrFltStore } from './useGcrFltStore';

export const GcrFltMessagePreviewContainer: React.FC = () => {
  const { message, clearMessage } = useGcrFltStore();
  const msgHeader = useMemo(() => message && formatGcrFltMsgHeader(message), [message]);
  const msgFooter = useMemo(() => message && formatScrMsgFooter(message), [message]);

  return <MessagePreview clearMessage={clearMessage} msgHeader={msgHeader} msgFooter={msgFooter} />;
};
