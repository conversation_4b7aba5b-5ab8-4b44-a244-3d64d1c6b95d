trigger:
  - none

pool:
  name: <pool name>

variables:
  - group: common

stages:
  - stage: InfrastructureDeploy
    jobs:
      - job: Infra
        steps:
  
          - task: AzureRmWebAppDeployment@4
            inputs:
              ConnectionType: 'AzureRM'
              azureSubscription: 'Azure PDC webhosting'
              appType: 'webApp'
              WebAppName: 'diyssim-test'
              VirtualApplication: 'fdfghfg'
              packageForLinux: '$(System.DefaultWorkingDirectory)/**/*.zip'