import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import { ErrorLabel } from '@src/components/FormComponents';
import type { Maybe } from '@src/typesGlobal';
import { gcrRegLineFormConfig } from './GcrRegLineFormConfig';
import { getGcrRegLineOnChange, getGcrRegOnKeyHandler } from './gcrRegLineFormUtils';
import { useGcrRegStore } from './gcrRegStore';
import type { GcrRegLine } from './gcrRegTypes';

const showError = (error: Maybe<string>) => <ErrorLabel error={error} />;

type GcrRegRegLineTextFieldProps = React.ComponentProps<typeof TextField> & {
  currentInputKey: keyof GcrRegLine;
};

export const GcrRegLineTextField: React.FC<GcrRegRegLineTextFieldProps> = (props) => {
  const { currentInputKey, ...rest } = props;
  const inputs = gcrRegLineFormConfig.inputs;
  const thisInput = inputs[currentInputKey];

  const {
    formLine,
    formLineErrors: errors,
    setFormLinePartial: updateLine,
    formLineFocusKey,
    setFormLineFocusKey,
  } = useGcrRegStore();
  // console.log(`errors`, errors);

  // Check if this input has options (making it a select field)
  const isSelect = thisInput.type === 'select';

  return (
    <TextField
      label={thisInput.label}
      name={thisInput.storeKey}
      select={isSelect}
      value={formLine?.[currentInputKey] || ''}
      onChange={getGcrRegLineOnChange(currentInputKey, updateLine)}
      onKeyDown={getGcrRegOnKeyHandler(inputs, currentInputKey, updateLine, setFormLineFocusKey)}
      focused={formLineFocusKey === currentInputKey}
      helperText={showError(errors?.[currentInputKey])}
      error={!!errors?.[currentInputKey]}
      {...rest}
    >
      {isSelect &&
        thisInput.options?.map((option) => (
          <MenuItem key={option} value={option}>
            {option}
          </MenuItem>
        ))}
    </TextField>
  );
};
