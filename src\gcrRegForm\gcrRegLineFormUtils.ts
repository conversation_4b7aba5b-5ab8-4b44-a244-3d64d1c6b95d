import { type BaseFormStore } from '@src/formStore/formStoreTypes';
import type { FormConfig } from '@src/typesGlobal';
import { gcrRegLineFormConfig } from './gcrRegLineFormConfig';
import type { GcrRegLine, GcrRegMessage } from './gcrRegTypes';

export type GcrRegLineInputs = FormConfig<GcrRegLine>['inputs'];
export type GcrRegUpdateLine = BaseFormStore<GcrRegLine, GcrRegMessage>['setFormLinePartial'];
export type GcrRegLineSetFocusKey = (key: keyof GcrRegLine | null) => void;

/**
 * 'getSrcLineOnChange' will given a field key and a function to update the line,
 * return a function that takes an event and updates the store for that field.
 */
export const getGcrRegLineOnChange =
  (key: keyof GcrRegLine, setFormLinePartial: GcrRegUpdateLine) => (e: React.ChangeEvent<HTMLInputElement>) => {
    let value: GcrRegLine[keyof GcrRegLine] = e.target.value;
    // Check if this field should be a number type
    if (gcrRegLineFormConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
      value = Number(e.target.value);
    }
    if (gcrRegLineFormConfig.inputs[key]?.toUpperCase) {
      value = e.target.value?.toUpperCase();
    }
    setFormLinePartial({ [key]: value });
  };

/** For an input, get the next input in the tab order  */
const getNextTabInput = (inputs: GcrRegLineInputs, currentKey: keyof GcrRegLine, reverse: boolean = false) => {
  const currentIndex = inputs[currentKey].tabOrder || 0;
  const nextIndex = reverse ? currentIndex - 1 : currentIndex + 1;
  const nextInput = Object.values(inputs).find((input) => input.tabOrder === nextIndex);
  return nextInput;
};

/**
 * For an input, get the onKey handler that handles tabbing and character entry.
 * Pressing tab will focus on the next input in the tab order,
 * and shift-tab will focus on the previous input in the tab order.
 * If the input is a select, it will also handle character entry to select an option.
 * */
export const getGcrRegOnKeyHandler =
  (
    inputs: GcrRegLineInputs,
    currentInputKey: keyof GcrRegLine,
    updateLine: GcrRegUpdateLine,
    setFocusKey: GcrRegLineSetFocusKey,
  ) =>
  (e: React.KeyboardEvent<HTMLElement>) => {
    // console.log(cloneDeep(e));
    const thisInput = inputs[currentInputKey];
    // console.log(`Character key: ${e.key}`);

    // If a character key is pressed, update state:
    if (thisInput.type === 'select' && e.key.length === 1 && !e.ctrlKey && !e.metaKey && !e.altKey) {
      const match = thisInput.options?.find((option) => option.toLowerCase() === e.key.toLowerCase());
      // console.log(`match`, match);
      if (match) updateLine?.({ [currentInputKey]: match });
    }

    // If tab is pressed, focus on next input
    if (e.key === 'Tab') {
      e.preventDefault();
      // e.stopPropagation();
      const nextInput = getNextTabInput(inputs, currentInputKey, e.shiftKey);
      setFocusKey(nextInput?.storeKey || null);
      if (nextInput) {
        const nextElement = document.querySelector(`[name="${nextInput.storeKey}"]`);
        // console.log(`nextElement`, nextElement);
        if (nextElement) (nextElement as HTMLInputElement)?.focus();
        nextElement?.setAttribute('aria-hidden', 'false');
      }
    }
  };
