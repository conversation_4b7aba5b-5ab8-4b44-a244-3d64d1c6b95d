import styled from '@emotion/styled';
import AppBar from '@mui/material/AppBar';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Paper from '@mui/material/Paper';
import { useTheme } from '@mui/material/styles';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import { appPages } from '@src/constants';
import { Link, Outlet, useLocation } from 'react-router-dom';

export const PageSectionWrap = styled(Paper)`
  overflow-x: auto;
  padding: 0.2rem;
  margin: 0 0 1.5rem 0;
  background-color: #f0f9ff;
  border: 1px solid #ccc;
`;

export const Layout: React.FC = () => {
  const theme = useTheme();
  const pageLocation = useLocation();
  const path = pageLocation.pathname;
  const activeColor = 'rgba(255, 255, 255, 0.1)';
  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h4" sx={{ flexGrow: 1 }}>
            Aviation Messages
          </Typography>
          {appPages.map((page) => (
            <Button
              key={page.name}
              color="inherit"
              component={Link}
              to={page.path}
              sx={{ bgcolor: path === page.path ? activeColor : 'transparent', fontSize: '0.9rem' }}
            >
              {page.name}
            </Button>
          ))}
        </Toolbar>
      </AppBar>
      <Container
        maxWidth={false}
        sx={{
          mt: 4,
          // bgcolor: 'lime',
          [theme.breakpoints.up('xs')]: {
            maxWidth: '100%',
          },
          [theme.breakpoints.up('sm')]: {
            maxWidth: '100%',
          },
          [theme.breakpoints.up('md')]: {
            maxWidth: '100%',
          },
          [theme.breakpoints.up('lg')]: {
            maxWidth: '100%',
          },
        }}
      >
        <Outlet />
      </Container>
    </>
  );
};
