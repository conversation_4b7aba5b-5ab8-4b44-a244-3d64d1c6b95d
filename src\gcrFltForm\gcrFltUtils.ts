import { isNil } from 'lodash';
import type { GcrFltLine, GcrFltMessage } from './types';

const val2str = <T = unknown>(val: T): string => (isNil(val) ? '' : String(val));

export const formatGcrFltLine = (line: GcrFltLine): string => {
  const action = val2str(line.action);
  const operator1 = val2str(line.operatorArr);
  const flightNumber1 = val2str(line.flightNumberArr?.padStart(4, '0'));
  const operator2 = val2str(line.operatorDep);
  const flightNumber2 = val2str(line.flightNumberDep?.padStart(4, '0'));
  const date = val2str(line.date);
  const seats = val2str(line.seats?.toString().padStart(3, '0'));
  const aircraftType = val2str(line.aircraftType);
  const origin = val2str(line.origin);
  const previous = val2str(line.previous);
  const time1 = val2str(line.timeArr);
  const time2 = val2str(line.timeDep);
  const on = line?.on === 0 ? '' : val2str(line?.on);
  const next = val2str(line.next);
  const destination = val2str(line.destination);
  const st1 = val2str(line.stArr);
  const st2 = val2str(line.stDep);
  const aircraftRegistration = `/ ${val2str(line?.aircraftRegistration)}/`;

  // let intro = operator1 ? `${action}${operator1}${flightNumber1}` : `${action}${operator2}${flightNumber2}`;
  // operator1 && operator2 && (intro += ` ${operator2}${flightNumber2}`);
  let result =
    // intro +
    `${action}${operator1}${flightNumber1}` +
    ` ${operator2}${flightNumber2}` +
    ` ${date} ${seats}${aircraftType}` +
    ` ${origin}${previous}${time1} ${time2}${on}${next}${destination}` +
    `${st1}${st2} ${aircraftRegistration}`;

  // Remove double spaces
  result = result.replace(/ {2,}/g, ' ').trim();

  // console.log({ result });
  return result;
};

export const formatGcrFltMsgHeader = (msg: GcrFltMessage): string => {
  const creator = msg.creator ? `\n/${msg.creator}` : '';
  return `GCRFLT${creator}\n${msg.season}\n${msg.date}\n${msg.airport}`;
};

export const formatScrMsgFooter = (msg: GcrFltMessage): string => {
  return `SI ${msg.si}\nGI ${msg.gi}`;
};

export const formatGcrFltMessage = (msg: GcrFltMessage, lines: GcrFltLine[]): string => {
  const header = formatGcrFltMsgHeader(msg);
  const linesAsStrings = lines.map(formatGcrFltLine);
  const footer = formatScrMsgFooter(msg);
  return [header, ...linesAsStrings, footer].join('\n');
};
