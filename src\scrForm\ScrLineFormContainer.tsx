import { zodErrorMap } from '@src/utils/utils';
import { stripEmptyStringsFromInput } from '@src/validation/utils';
import { ScrActionValues, serviceTypeValues } from '@src/validation/validationSchemas';
import { useCallback, useMemo } from 'react';
import { ScrLineFormTableRenderer } from './ScrLineFormTableRenderer';
import { type ScrLine } from './types';
import type { FormSrcLineConfig } from './typesForm';
import { useScrStore } from './useScrStore';
import { scrLineSchema } from './validation';

/**
 * The 'ScrLineFormContainer' creates config and handlers and passes
 * them to the 'ScrLineFormTableRenderer' component.
 */
export const ScrLineFormContainer: React.FC = () => {
  const {
    addLine,
    updateLine,
    formLine,
    setFormLinePartial,
    formLineIndex,
    clearFormLine,
    setFormLineErrors,
    clearFormLineErrors,
  } = useScrStore();

  const isEdit = formLineIndex !== null;

  /**
   * 'handleAddLine' will validate the formLine and add it to the store.
   * Memoize it to prevent unnecessary re-renders.
   */
  const handleAddLine = useCallback(() => {
    const cleanedFormLine = stripEmptyStringsFromInput(formLine);
    const parsed = scrLineSchema.safeParse(cleanedFormLine);
    if (!parsed.success) {
      const errorMap = zodErrorMap(parsed.error);
      // console.log(`errorMap`, errorMap);
      setFormLineErrors(errorMap);
      return;
    }
    clearFormLineErrors();
    isEdit ? updateLine(parsed.data) : addLine(parsed.data);
    clearFormLine();
  }, [addLine, clearFormLine, clearFormLineErrors, formLine, isEdit, setFormLineErrors, updateLine]);

  /**
   * 'formConfig' is memoized to prevent unnecessary re-renders.
   * It contains the configuration for the form inputs.
   */
  const formConfig: FormSrcLineConfig = useMemo(
    () => ({
      inputs: {
        action: {
          storeKey: 'action',
          label: 'Action',
          type: 'select',
          options: ScrActionValues,
        },
        operator1: {
          storeKey: 'operator1',
          label: '',
          type: 'text',
          toUpperCase: true,
        },
        flightNumber1: {
          storeKey: 'flightNumber1',
          label: '',
          type: 'text',
          toUpperCase: true,
        },
        operator2: {
          storeKey: 'operator2',
          label: 'Secondary Operator',
          type: 'text',
          toUpperCase: true,
        },
        flightNumber2: {
          storeKey: 'flightNumber2',
          label: 'Secondary Flight Number',
          type: 'text',
          toUpperCase: true,
        },
        dateFrom: {
          storeKey: 'dateFrom',
          label: 'From',
          type: 'text',
          toUpperCase: true,
        },
        dateTo: {
          storeKey: 'dateTo',
          label: 'To',
          type: 'text',
          toUpperCase: true,
        },
        doop: {
          storeKey: 'doop',
          label: '',
          type: 'text',
        },
        seats: {
          storeKey: 'seats',
          label: '',
          type: 'number',
        },
        aircraftType: {
          storeKey: 'aircraftType',
          label: '',
          type: 'text',
        },
        origin: {
          storeKey: 'origin',
          label: '',
          type: 'text',
          toUpperCase: true,
        },
        previous: {
          storeKey: 'previous',
          label: '',
          type: 'text',
          toUpperCase: true,
        },
        time1: {
          storeKey: 'time1',
          label: '',
          type: 'text',
        },
        time2: {
          storeKey: 'time2',
          label: '',
          type: 'text',
        },
        on: {
          storeKey: 'on',
          label: '',
          type: 'number',
        },
        next: {
          storeKey: 'next',
          label: '',
          type: 'text',
          toUpperCase: true,
        },
        destination: {
          storeKey: 'destination',
          label: '',
          type: 'text',
          toUpperCase: true,
        },
        st1: {
          storeKey: 'st1',
          label: '',
          type: 'select',
          options: serviceTypeValues,
        },
        st2: {
          storeKey: 'st2',
          label: '',
          type: 'select',
          options: serviceTypeValues,
        },
        frequency: {
          storeKey: 'frequency',
          type: 'text',
          label: '',
          toUpperCase: true,
        },
        aircraftRegistration: {
          storeKey: 'aircraftRegistration',
          type: 'text',
          label: '',
          toUpperCase: true,
        },
      },
    }),
    [],
  );

  /**
   * 'getOnChange' will given a field key, return a function that takes an event
   * and updates the store for that field.
   * Memoize it to prevent unnecessary re-renders.
   */
  const getOnChange = useCallback(
    (key: keyof ScrLine) => (e: React.ChangeEvent<HTMLInputElement>) => {
      let value: ScrLine[keyof ScrLine] = e.target.value;
      // Check if this field should be a number type
      if (formConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
        value = Number(e.target.value);
      }
      if (formConfig.inputs[key]?.toUpperCase) {
        value = e.target.value?.toUpperCase();
      }
      setFormLinePartial({ [key]: value });
    },
    [formConfig.inputs, setFormLinePartial],
  );

  return <ScrLineFormTableRenderer formConfig={formConfig} getOnChange={getOnChange} handleAddLine={handleAddLine} />;
};
