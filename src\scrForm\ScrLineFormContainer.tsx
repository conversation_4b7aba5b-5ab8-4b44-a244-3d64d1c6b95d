import type { FormConfig } from '@src/typesGlobal';
import { zodErrorMap } from '@src/utils/utils';
import { stripEmptyStringsFromInput } from '@src/validation/utils';
import { ScrActionValues, serviceTypeValues } from '@src/validation/validationSchemas';
import { useCallback, useMemo } from 'react';
import { ScrLineFormTableRenderer } from './ScrLineFormTableRenderer';
import { type ScrLine } from './scrTypes';
import { scrLineSchema } from './scrValidation';
import { useScrStore } from './useScrStore';

/* SCR tab order:
1.	action
2.	operatorArr
3.	operatorDep
4.	flightNumberArr
5.	flightNumberDep
6.	dateFrom
7.	dateTo
8.	doop
9.	seats
10.	aircraftType
11.	origin
12.	previous
13.	timeArr
14.	timeDep
15.	on
16.	next
17.	destination
18.	stArr
19.	stDep
20.	frequency
21.	aircraftRegistration
 */

/**
 * The 'ScrLineFormContainer' creates config and handlers and passes
 * them to the 'ScrLineFormTableRenderer' component.
 */
export const ScrLineFormContainer: React.FC = () => {
  const {
    addLine,
    updateLine,
    formLine,
    setFormLinePartial,
    formLineIndex,
    clearFormLine,
    setFormLineErrors,
    clearFormLineErrors,
  } = useScrStore();

  const isEdit = formLineIndex !== null;

  /**
   * 'handleAddLine' will validate the formLine and add it to the store.
   * Memoize it to prevent unnecessary re-renders.
   */
  const handleAddLine = useCallback(() => {
    const cleanedFormLine = stripEmptyStringsFromInput(formLine);
    const parsed = scrLineSchema.safeParse(cleanedFormLine);
    if (!parsed.success) {
      const errorMap = zodErrorMap(parsed.error);
      // console.log(`errorMap`, errorMap);
      setFormLineErrors(errorMap);
      return;
    }
    clearFormLineErrors();
    isEdit ? updateLine(parsed.data) : addLine(parsed.data);
    clearFormLine();
  }, [addLine, clearFormLine, clearFormLineErrors, formLine, isEdit, setFormLineErrors, updateLine]);

  /**
   * 'formConfig' is memoized to prevent unnecessary re-renders.
   * It contains the configuration for the form inputs.
   */
  const formConfig: FormConfig<ScrLine> = useMemo(
    () => ({
      inputs: {
        action: {
          storeKey: 'action',
          label: 'Action',
          type: 'select',
          options: ScrActionValues,
          tabOrder: 1,
        },
        operatorArr: {
          storeKey: 'operatorArr',
          label: '',
          type: 'text',
          toUpperCase: true,
          tabOrder: 2,
        },
        flightNumberArr: {
          storeKey: 'flightNumberArr',
          label: '',
          type: 'text',
          toUpperCase: true,
          tabOrder: 3,
        },
        operatorDep: {
          storeKey: 'operatorDep',
          label: '',
          type: 'text',
          toUpperCase: true,
          tabOrder: 4,
        },
        flightNumberDep: {
          storeKey: 'flightNumberDep',
          label: '',
          type: 'text',
          toUpperCase: true,
          tabOrder: 5,
        },
        dateFrom: {
          storeKey: 'dateFrom',
          label: 'From',
          type: 'text',
          toUpperCase: true,
          tabOrder: 6,
        },
        dateTo: {
          storeKey: 'dateTo',
          label: 'To',
          type: 'text',
          toUpperCase: true,
          tabOrder: 7,
        },
        doop: {
          storeKey: 'doop',
          label: '',
          type: 'text',
          tabOrder: 8,
        },
        seats: {
          storeKey: 'seats',
          label: '',
          type: 'number',
          tabOrder: 9,
        },
        aircraftType: {
          storeKey: 'aircraftType',
          label: '',
          type: 'text',
          tabOrder: 10,
        },
        origin: {
          storeKey: 'origin',
          label: '',
          type: 'text',
          toUpperCase: true,
          tabOrder: 11,
        },
        previous: {
          storeKey: 'previous',
          label: '',
          type: 'text',
          toUpperCase: true,
          tabOrder: 12,
        },
        timeArr: {
          storeKey: 'timeArr',
          label: '',
          type: 'text',
          tabOrder: 13,
        },
        timeDep: {
          storeKey: 'timeDep',
          label: '',
          type: 'text',
          tabOrder: 14,
        },
        on: {
          storeKey: 'on',
          label: '',
          type: 'number',
          tabOrder: 15,
        },
        next: {
          storeKey: 'next',
          label: '',
          type: 'text',
          toUpperCase: true,
          tabOrder: 16,
        },
        destination: {
          storeKey: 'destination',
          label: '',
          type: 'text',
          toUpperCase: true,
          tabOrder: 17,
        },
        stArr: {
          storeKey: 'stArr',
          label: '',
          type: 'select',
          options: serviceTypeValues,
          tabOrder: 18,
        },
        stDep: {
          storeKey: 'stDep',
          label: '',
          type: 'select',
          options: serviceTypeValues,
          tabOrder: 19,
        },
        frequency: {
          storeKey: 'frequency',
          type: 'text',
          label: '',
          toUpperCase: true,
          tabOrder: 20,
        },
        aircraftRegistration: {
          storeKey: 'aircraftRegistration',
          type: 'text',
          label: '',
          toUpperCase: true,
          tabOrder: 21,
        },
      },
    }),
    [],
  );

  /**
   * 'getOnChange' will given a field key, return a function that takes an event
   * and updates the store for that field.
   * Memoize it to prevent unnecessary re-renders.
   */
  const getOnChange = useCallback(
    (key: keyof ScrLine) => (e: React.ChangeEvent<HTMLInputElement>) => {
      let value: ScrLine[keyof ScrLine] = e.target.value;
      // Check if this field should be a number type
      if (formConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
        value = Number(e.target.value);
      }
      if (formConfig.inputs[key]?.toUpperCase) {
        value = e.target.value?.toUpperCase();
      }
      setFormLinePartial({ [key]: value });
    },
    [formConfig.inputs, setFormLinePartial],
  );

  return <ScrLineFormTableRenderer formConfig={formConfig} getOnChange={getOnChange} handleAddLine={handleAddLine} />;
};
