import { zodErrorMap } from '@src/utils/utils';
import { stripEmptyStringsFromInput } from '@src/validation/utils';
import { useCallback } from 'react';
import { scrLineFormConfig } from './ScrLineFormConfig';
import { ScrLineFormTableRenderer } from './ScrLineFormTableRenderer';
import { type ScrLine } from './scrTypes';
import { scrLineSchema } from './scrValidation';
import { useScrStore } from './useScrStore';

/**
 * The 'ScrLineFormContainer' creates config and handlers and passes
 * them to the 'ScrLineFormTableRenderer' component.
 */
export const ScrLineFormContainer: React.FC = () => {
  const {
    addLine,
    updateLine,
    formLine,
    setFormLinePartial,
    formLineIndex,
    clearFormLine,
    setFormLineErrors,
    clearFormLineErrors,
  } = useScrStore();

  const isEdit = formLineIndex !== null;

  /**
   * 'handleAddLine' will validate the formLine and add it to the store.
   * Memoize it to prevent unnecessary re-renders.
   */
  const handleAddLine = useCallback(() => {
    const cleanedFormLine = stripEmptyStringsFromInput(formLine);
    const parsed = scrLineSchema.safeParse(cleanedFormLine);
    if (!parsed.success) {
      const errorMap = zodErrorMap(parsed.error);
      // console.log(`errorMap`, errorMap);
      setFormLineErrors(errorMap);
      return;
    }
    clearFormLineErrors();
    isEdit ? updateLine(parsed.data) : addLine(parsed.data);
    clearFormLine();
  }, [addLine, clearFormLine, clearFormLineErrors, formLine, isEdit, setFormLineErrors, updateLine]);

  /**
   * 'getOnChange' will given a field key, return a function that takes an event
   * and updates the store for that field.
   * Memoize it to prevent unnecessary re-renders.
   */
  const getOnChange = useCallback(
    (key: keyof ScrLine) => (e: React.ChangeEvent<HTMLInputElement>) => {
      let value: ScrLine[keyof ScrLine] = e.target.value;
      // Check if this field should be a number type
      if (scrLineFormConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
        value = Number(e.target.value);
      }
      if (scrLineFormConfig.inputs[key]?.toUpperCase) {
        value = e.target.value?.toUpperCase();
      }
      setFormLinePartial({ [key]: value });
    },
    [setFormLinePartial],
  );

  return (
    <ScrLineFormTableRenderer formConfig={scrLineFormConfig} getOnChange={getOnChange} handleAddLine={handleAddLine} />
  );
};
