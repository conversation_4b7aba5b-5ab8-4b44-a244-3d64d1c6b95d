import { zodErrorMap } from '@src/utils/utils';
import { stripEmptyStringsFromInput } from '@src/validation/utils';
import { useCallback } from 'react';
import { gcrFltLineFormConfig } from './gcrFltLineFormConfig';
import { GcrFltLineFormTableRenderer } from './GcrFltLineFormTableRenderer';
import { type GcrFltLine } from './gcrFltTypes';
import { gcrFltLineSchema } from './gcrFltValidation';
import { useGcrFltStore } from './useGcrFltStore';

/**
 * The 'GcrFltLineFormContainer' creates config and handlers for the line form
 * and passes them to the 'GcrFltLineFormTableRenderer'
 */
export const GcrFltLineFormContainer: React.FC = () => {
  const {
    addLine,
    updateLine,
    formLine,
    setFormLinePartial,
    formLineIndex,
    clearFormLine,
    setFormLineErrors,
    clearFormLineErrors,
  } = useGcrFltStore();

  const isEdit = formLineIndex !== null;

  /**
   * 'handleAddLine' will validate the formLine and add it to the store.
   * Memoize it to prevent unnecessary re-renders.
   */
  const handleAddLine = useCallback(() => {
    const cleanedFormLine = stripEmptyStringsFromInput(formLine);
    // console.log(`cleanedFormLine;`, JSON.stringify(cleanedFormLine, null, 2));
    const parsed = gcrFltLineSchema.safeParse(cleanedFormLine);
    if (!parsed.success) {
      const errorMap = zodErrorMap(parsed.error);
      // console.log(`errorMap`, errorMap);
      setFormLineErrors(errorMap);
      return;
    }
    clearFormLineErrors();
    isEdit ? updateLine(parsed.data) : addLine(parsed.data);
    clearFormLine();
  }, [addLine, clearFormLine, clearFormLineErrors, formLine, isEdit, setFormLineErrors, updateLine]);

  /**
   * 'getOnChange' will given a field key, return a function that takes an event
   * and updates the store for that field.
   * Memoize it to prevent unnecessary re-renders.
   */
  const getOnChange = useCallback(
    (key: keyof GcrFltLine) => (e: React.ChangeEvent<HTMLInputElement>) => {
      // console.log(`getOnChange`, key, e.target.value);
      let value: GcrFltLine[keyof GcrFltLine] = e.target.value;
      // Check if this field should be a number type
      if (gcrFltLineFormConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
        value = Number(e.target.value);
      }
      if (gcrFltLineFormConfig.inputs[key]?.toUpperCase) {
        value = e.target.value?.toUpperCase();
      }
      setFormLinePartial({ [key]: value });
    },
    [setFormLinePartial],
  );

  return (
    <GcrFltLineFormTableRenderer
      formConfig={gcrFltLineFormConfig}
      getOnChange={getOnChange}
      handleAddLine={handleAddLine}
    />
  );
};
