import { useCallback, useMemo } from 'react';
import type { FormInputEvent, FormSrcMsgConfig } from './typesForm';
import { ScrMessageFormTableRenderer } from './ScrMessageFormTableRenderer';
import type { ScrMessage } from './types';
// import { useMsgFormHandlers } from './useFormHandlers';
import { useScrStore } from './useScrStore';

/**
 * The 'ScrMessageFormContainer' creates config and handlers for the form inputs
 * and passes them to the 'ScrMessageFormTableRenderer' for rendering.
 */
export const ScrMessageFormContainer: React.FC = () => {
  const { setFormMessagePartial } = useScrStore();

  /**
   * 'formConfig' is memoized to prevent unnecessary re-renders.
   * It contains the configuration for the form inputs.
   */
  const formConfig: FormSrcMsgConfig = useMemo(
    () => ({
      inputs: {
        season: {
          storeKey: 'season',
          label: 'Season',
          type: 'text',
          toUpperCase: true,
        },
        date: {
          storeKey: 'date',
          label: 'Date',
          type: 'text',
          toUpperCase: true,
        },
        airport: {
          storeKey: 'airport',
          label: 'Airport',
          type: 'text',
          toUpperCase: true,
        },
        creator: {
          storeKey: 'creator',
          label: 'Creator',
          type: 'text',
        },
        si: {
          storeKey: 'si',
          label: 'SI',
          type: 'text',
        },
        gi: {
          storeKey: 'gi',
          label: 'GI',
          type: 'text',
        },
      },
    }),
    [],
  );

  /**
   * 'getOnChange' will given a field key, return a function that takes an event
   * and updates the store for that field.
   * Memoize it to prevent unnecessary re-renders.
   */
  const getOnChange = useCallback(
    (key: keyof ScrMessage) => (e: FormInputEvent) => {
      let value: ScrMessage[keyof ScrMessage] = e.target.value;
      // Check if this field should be uppercased
      if (formConfig.inputs[key]?.toUpperCase) {
        value = e.target.value?.toUpperCase();
      }
      // Check if this field should be a number type
      // if (formConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
      //   value = Number(e.target.value);
      // }
      setFormMessagePartial({ [key]: value });
    },
    [formConfig.inputs, setFormMessagePartial],
  );

  return <ScrMessageFormTableRenderer formConfig={formConfig} getOnChange={getOnChange} />;
};
