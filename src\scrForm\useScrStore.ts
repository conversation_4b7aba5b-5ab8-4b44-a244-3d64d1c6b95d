import { createFormStore, createEmptyMessageWithDate } from '@src/utils/createFormStore';
import type { ScrLine, ScrLineFormInput, ScrMessage } from './types';

const emptyLine: ScrLineFormInput = { action: 'N' };
const emptyMessage: Partial<ScrMessage> = createEmptyMessageWithDate();

export const useScrStore = createFormStore<ScrLine, ScrMessage, ScrLineFormInput>({
  emptyLine,
  emptyMessage,
});
