import { Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import { Layout } from './components/PageLayout';
import { appPages } from './constants';

export const App: React.FC = () => {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          {appPages.map((page) => (
            <Route key={page.path} path={page.path} element={<page.component />} />
          ))}
        </Route>
      </Routes>
    </Router>
  );
};
