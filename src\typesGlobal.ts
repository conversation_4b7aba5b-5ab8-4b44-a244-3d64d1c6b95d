export type Maybe<T> = T | null | undefined;

// Form types
export type BaseFieldSet<T = unknown> = Record<string, T>;

// Store types
export type BaseFormStore<TLine, TMessage> = {
  message: TMessage | null;
  setMessage: (message: TMessage) => void;
  clearMessage: () => void;

  formMessage: Partial<TMessage> | null;
  setFormMessagePartial: (updates: Partial<TMessage>) => void;
  clearFormMessage: () => void;

  formMessageErrors: Record<string, string> | null;
  setFormMessageErrors: (error: Record<string, string> | null) => void;
  clearFormMessageErrors: () => void;

  lines: TLine[];
  addLine: (line: TLine) => void;
  updateLine: (updates: Partial<TLine>) => void;
  deleteLine: (index: number) => void;
  // TODO: clearAllLines currently not used, remove or implement?
  clearAllLines: () => void;

  formLine: Partial<TLine> | null;
  formLineIndex: number | null;
  setFormLine: (index?: number | null) => void;
  setFormLinePartial: (updates: Partial<TLine>, clear?: boolean) => void;
  clearFormLine: () => void;

  formLineErrors: Record<string, string> | null;
  setFormLineErrors: (errors: Record<string, string> | null) => void;
  clearFormLineErrors: () => void;
};

// Immer-compatible version for internal use in factories
export type ImmerFormStore<TLine, TMessage> = {
  message: TMessage | null;
  setMessage: (message: TMessage) => void;
  clearMessage: () => void;

  formMessage: Partial<TMessage> | null;
  setFormMessagePartial: (updates: Partial<TMessage>) => void;
  clearFormMessage: () => void;

  formMessageErrors: Record<string, string> | null;
  setFormMessageErrors: (error: Record<string, string> | null) => void;
  clearFormMessageErrors: () => void;

  lines: TLine[];
  addLine: (line: TLine) => void;
  updateLine: (updates: Partial<TLine>) => void;
  deleteLine: (index: number) => void;
  clearAllLines: () => void;

  formLine: Partial<TLine> | null;
  formLineIndex: number | null;
  setFormLine: (index?: number | null) => void;
  setFormLinePartial: (updates: Partial<TLine>, clear?: boolean) => void;
  clearFormLine: () => void;

  formLineErrors: Record<string, string> | null;
  setFormLineErrors: (errors: Record<string, string> | null) => void;
  clearFormLineErrors: () => void;
};
