import Box from '@mui/material/Box';
import MenuItem from '@mui/material/MenuItem';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import { ErrorLabel, FormButton, MockButton, TableFormInnerBox } from '@src/components/FormComponents';
import { DEBUG } from '@src/constants';
import type { FormConfig, Maybe } from '@src/typesGlobal';
import { formatDoop } from '@src/utils/utils';
import { mockLines } from './scrMockData';
import type { ScrLine } from './scrTypes';
import { useScrStore } from './useScrStore';

const showError = (error: Maybe<string>) => <ErrorLabel error={error} />;

type ScrLineFormProps = {
  formConfig: FormConfig<ScrLine>;
  getOnChange: (key: keyof ScrLine) => (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleAddLine: () => void;
};

export const ScrLineFormTableRenderer: React.FC<ScrLineFormProps> = (props) => {
  const { formConfig, getOnChange, handleAddLine } = props;
  const { inputs } = formConfig;

  const { formLine, formLineIndex, clearFormLine, formLineErrors: errors, setFormLinePartial } = useScrStore();
  // console.log(`errors`, errors);

  const isEdit = formLineIndex !== null;

  return (
    <>
      <Table size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <colgroup>
          <col style={{ width: '2%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '8%' }} />
          {/* 5 */}
          <col style={{ width: '5%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          {/* 10 */}
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '3%' }} />
          {/* 15 */}
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell></TableCell>
            <TableCell align="center">C/R/N/D</TableCell>
            <TableCell>Operator</TableCell>
            <TableCell>FltNo</TableCell>
            <TableCell sx={{ minWidth: '10rem' }}>Date Range</TableCell>
            {/* 5 */}
            <TableCell>DOOP</TableCell>
            <TableCell align="center">Seats</TableCell>
            <TableCell align="center">A/C</TableCell>
            <TableCell align="center">Origin</TableCell>
            <TableCell align="center">Prev</TableCell>
            {/* 10 */}
            <TableCell align="center">Time</TableCell>
            <TableCell align="center">O/N</TableCell>
            <TableCell align="center">Next</TableCell>
            <TableCell align="center">Dest</TableCell>
            <TableCell align="center">St</TableCell>
            {/* 15 */}
            <TableCell align="center">Frq</TableCell>
            <TableCell align="center">Acreg</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell component="th" scope="row">
              Arr
            </TableCell>
            {/* Action */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs.action.label}
                name={inputs.action.storeKey}
                select
                value={formLine?.action || ''}
                onChange={getOnChange('action')}
                error={!!errors?.[inputs.action.storeKey]}
                helperText={showError(errors?.[inputs.action.storeKey])}
                slotProps={{
                  select: {
                    slotProps: { input: { tabIndex: inputs.action.tabOrder } },
                  },
                }}
              >
                {inputs.action.options?.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </TableCell>
            {/* Operator */}
            <TableCell>
              <TextField
                fullWidth
                label={inputs?.operatorArr?.label}
                name={inputs?.operatorArr?.storeKey}
                value={formLine?.operatorArr || ''}
                onChange={getOnChange('operatorArr')}
                error={!!errors?.[inputs.operatorArr.storeKey]}
                helperText={showError(errors?.[inputs.operatorArr.storeKey])}
                slotProps={{ input: { tabIndex: inputs.operatorArr.tabOrder } }}
              />
            </TableCell>
            {/* Flight Number */}
            <TableCell>
              <TextField
                label={inputs?.flightNumberArr?.label}
                name={inputs?.flightNumberArr?.storeKey}
                value={formLine?.flightNumberArr || ''}
                onChange={getOnChange('flightNumberArr')}
                error={!!errors?.[inputs.flightNumberArr.storeKey]}
                helperText={showError(errors?.[inputs.flightNumberArr.storeKey])}
                slotProps={{ input: { tabIndex: inputs.flightNumberArr.tabOrder } }}
              />
            </TableCell>
            {/* Date Range */}
            <TableCell rowSpan={2}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  label={inputs?.dateFrom?.label}
                  name={inputs?.dateFrom?.storeKey}
                  value={formLine?.dateFrom || ''}
                  onChange={getOnChange('dateFrom')}
                  error={!!errors?.[inputs.dateFrom.storeKey]}
                  helperText={showError(errors?.[inputs.dateFrom.storeKey])}
                  sx={{ maxWidth: '5rem' }}
                  slotProps={{ input: { tabIndex: inputs.dateFrom.tabOrder } }}
                />
                <TextField
                  label={inputs?.dateTo?.label}
                  name={inputs?.dateTo?.storeKey}
                  value={formLine?.dateTo || ''}
                  onChange={getOnChange('dateTo')}
                  error={!!errors?.[inputs.dateTo.storeKey]}
                  helperText={showError(errors?.[inputs.dateTo.storeKey])}
                  sx={{ maxWidth: '5rem' }}
                  slotProps={{ input: { tabIndex: inputs.dateTo.tabOrder } }}
                />
              </Box>
            </TableCell>
            {/* DOOP */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.doop?.label}
                name={inputs?.doop?.storeKey}
                value={formLine?.doop || ''}
                onChange={getOnChange('doop')}
                onBlur={(e) => {
                  // Convert DOOP shorthand to full format on blur
                  setFormLinePartial({ doop: formatDoop(e.target.value) });
                }}
                error={!!errors?.[inputs.doop.storeKey]}
                helperText={showError(errors?.[inputs.doop.storeKey])}
                slotProps={{ input: { tabIndex: inputs.doop.tabOrder } }}
              />
            </TableCell>
            {/* Seats */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.seats?.label}
                name={inputs?.seats?.storeKey}
                value={formLine?.seats || ''}
                onChange={getOnChange('seats')}
                error={!!errors?.[inputs.seats.storeKey]}
                helperText={showError(errors?.[inputs.seats.storeKey])}
                slotProps={{ input: { tabIndex: inputs.seats.tabOrder } }}
              />
            </TableCell>
            {/* Aircraft Type */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.aircraftType?.label}
                name={inputs?.aircraftType?.storeKey}
                value={formLine?.aircraftType || ''}
                onChange={getOnChange('aircraftType')}
                error={!!errors?.[inputs.aircraftType.storeKey]}
                helperText={showError(errors?.[inputs.aircraftType.storeKey])}
                slotProps={{ input: { tabIndex: inputs.aircraftType.tabOrder } }}
              />
            </TableCell>
            {/* Origin */}
            <TableCell>
              <TextField
                label={inputs?.origin?.label}
                name={inputs?.origin?.storeKey}
                value={formLine?.origin || ''}
                onChange={getOnChange('origin')}
                error={!!errors?.[inputs.origin.storeKey]}
                helperText={showError(errors?.[inputs.origin.storeKey])}
                slotProps={{ input: { tabIndex: inputs.origin.tabOrder } }}
              />
            </TableCell>
            {/* Previous */}
            <TableCell>
              <TextField
                label={inputs?.previous?.label}
                name={inputs?.previous?.storeKey}
                value={formLine?.previous || ''}
                onChange={getOnChange('previous')}
                error={!!errors?.[inputs.previous.storeKey]}
                helperText={showError(errors?.[inputs.previous.storeKey])}
                slotProps={{ input: { tabIndex: inputs.previous.tabOrder } }}
              />
            </TableCell>
            {/* Time 1 */}
            <TableCell>
              <TextField
                label={inputs?.timeArr?.label}
                name={inputs?.timeArr?.storeKey}
                value={formLine?.timeArr || ''}
                onChange={getOnChange('timeArr')}
                error={!!errors?.[inputs.timeArr.storeKey]}
                helperText={showError(errors?.[inputs.timeArr.storeKey])}
                slotProps={{ input: { tabIndex: inputs.timeArr.tabOrder } }}
              />
            </TableCell>
            {/* O/N */}
            <TableCell>{/* O/N */}</TableCell>
            {/* Next */}
            <TableCell>{/* Next */}</TableCell>
            {/* Destination */}
            <TableCell>{/* Dest */}</TableCell>
            {/* Service Type*/}
            <TableCell>
              <TextField
                label={inputs?.stArr?.label}
                name={inputs?.stArr?.storeKey}
                value={formLine?.stArr || ''}
                onChange={getOnChange('stArr')}
                select
                error={!!errors?.[inputs.stArr.storeKey]}
                helperText={showError(errors?.[inputs.stArr.storeKey])}
                slotProps={{
                  select: {
                    slotProps: { input: { tabIndex: inputs.stArr.tabOrder } },
                  },
                }}
              >
                {inputs?.stArr?.options?.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </TableCell>
            {/* Frequency */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.frequency?.label}
                name={inputs?.frequency?.storeKey}
                value={formLine?.frequency || ''}
                onChange={getOnChange('frequency')}
                error={!!errors?.[inputs.frequency.storeKey]}
                helperText={showError(errors?.[inputs.frequency.storeKey])}
                slotProps={{ input: { tabIndex: inputs.frequency.tabOrder } }}
              />
            </TableCell>
            {/* Aircraft Registration */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.aircraftRegistration?.label}
                name={inputs?.aircraftRegistration?.storeKey}
                value={formLine?.aircraftRegistration || ''}
                onChange={getOnChange('aircraftRegistration')}
                error={!!errors?.[inputs.aircraftRegistration.storeKey]}
                helperText={showError(errors?.[inputs.aircraftRegistration.storeKey])}
                slotProps={{ input: { tabIndex: inputs.aircraftRegistration.tabOrder } }}
              />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell component="th" scope="row">
              Dep
            </TableCell>
            {/* Action */}
            {/* rowSpan={2} */}
            {/* Operator */}
            <TableCell>
              <TextField
                label={inputs?.operatorDep?.label}
                name={inputs?.operatorDep?.storeKey}
                value={formLine?.operatorDep || ''}
                onChange={getOnChange('operatorDep')}
                error={!!errors?.[inputs.operatorDep.storeKey]}
                helperText={showError(errors?.[inputs.operatorDep.storeKey])}
                tabIndex={inputs.operatorDep.tabOrder}
              />
            </TableCell>
            {/* Flight Number */}
            <TableCell>
              <TextField
                label={inputs?.flightNumberDep?.label}
                name={inputs?.flightNumberDep?.storeKey}
                value={formLine?.flightNumberDep || ''}
                onChange={getOnChange('flightNumberDep')}
                error={!!errors?.[inputs.flightNumberDep.storeKey]}
                helperText={showError(errors?.[inputs.flightNumberDep.storeKey])}
                tabIndex={inputs.flightNumberDep.tabOrder}
              />
            </TableCell>
            {/* Date Range */}
            {/* rowSpan={2} */}
            {/* DOOP */}
            {/* rowSpan={2} */}
            {/* Seats */}
            {/* rowSpan={2} */}
            {/* Aircraft Type */}
            {/* rowSpan={2} */}
            {/* Origin */}
            <TableCell>{/* Origin */}</TableCell>
            {/* Previous */}
            <TableCell>{/* Prev */}</TableCell>
            {/* Time 2 */}
            <TableCell>
              <TextField
                label={inputs?.timeDep?.label}
                name={inputs?.timeDep?.storeKey}
                value={formLine?.timeDep || ''}
                onChange={getOnChange('timeDep')}
                error={!!errors?.[inputs.timeDep.storeKey]}
                helperText={showError(errors?.[inputs.timeDep.storeKey])}
                tabIndex={inputs.timeDep.tabOrder}
              />
            </TableCell>
            {/* O/N */}
            <TableCell>
              {/* O/N */}
              <TextField
                label={inputs?.on?.label}
                name={inputs?.on?.storeKey}
                value={formLine?.on || ''}
                onChange={getOnChange('on')}
                error={!!errors?.[inputs.on.storeKey]}
                helperText={showError(errors?.[inputs.on.storeKey])}
                tabIndex={inputs.on.tabOrder}
              />
            </TableCell>
            {/* Next */}
            <TableCell>
              {/* Next */}
              <TextField
                label={inputs?.next?.label}
                name={inputs?.next?.storeKey}
                value={formLine?.next || ''}
                onChange={getOnChange('next')}
                error={!!errors?.[inputs.next.storeKey]}
                helperText={showError(errors?.[inputs.next.storeKey])}
                tabIndex={inputs.next.tabOrder}
              />
            </TableCell>
            {/* Destination */}
            <TableCell>
              {/* Dest */}
              <TextField
                label={inputs?.destination?.label}
                name={inputs?.destination?.storeKey}
                value={formLine?.destination || ''}
                onChange={getOnChange('destination')}
                error={!!errors?.[inputs.destination.storeKey]}
                helperText={showError(errors?.[inputs.destination.storeKey])}
                tabIndex={inputs.destination.tabOrder}
              />
            </TableCell>
            {/* Service Type*/}
            <TableCell>
              <TextField
                label={inputs?.stDep?.label}
                name={inputs?.stDep?.storeKey}
                value={formLine?.stDep || ''}
                onChange={getOnChange('stDep')}
                select
                error={!!errors?.[inputs.stDep.storeKey]}
                helperText={showError(errors?.[inputs.stDep.storeKey])}
                tabIndex={inputs.stDep.tabOrder}
              >
                {inputs?.stDep?.options?.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </TableCell>
            {/* Frequency */}
            {/* rowSpan={2} */}
            {/* Aircraft Registration */}
            {/* rowSpan={2} */}
          </TableRow>
        </TableBody>
      </Table>

      {DEBUG && (
        <TableFormInnerBox>
          {mockLines.map((line, i) => (
            <MockButton key={i} onClick={() => setFormLinePartial({ ...line })}>
              {i}
            </MockButton>
          ))}
        </TableFormInnerBox>
      )}

      <TableFormInnerBox>
        <FormButton variant="contained" size="small" onClick={handleAddLine}>
          {isEdit ? `Update Line` : `Add Line`}
        </FormButton>
        <FormButton variant="contained" color="warning" size="small" onClick={() => clearFormLine()}>
          Clear
        </FormButton>
      </TableFormInnerBox>
    </>
  );
};
