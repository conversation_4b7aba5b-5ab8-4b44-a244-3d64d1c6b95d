import Box from '@mui/material/Box';
import MenuItem from '@mui/material/MenuItem';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import { ErrorLabel, FormButton, MockButton, TableFormInnerBox } from '@src/components/FormComponents';
import { DEBUG } from '@src/constants';
import type { Maybe } from '@src/typesGlobal';
import { formatDoop } from '@src/utils/utils';
import { mockLines } from './mockData';
import type { ScrLine } from './types';
import type { FormSrcLineConfig } from './typesForm';
import { useScrStore } from './useScrStore';

const showError = (error: Maybe<string>) => <ErrorLabel error={error} />;

type ScrLineFormProps = {
  formConfig: FormSrcLineConfig;
  getOnChange: (key: keyof ScrLine) => (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleAddLine: () => void;
};

export const ScrLineFormTableRenderer: React.FC<ScrLineFormProps> = (props) => {
  const { formConfig, getOnChange, handleAddLine } = props;
  const { inputs } = formConfig;

  const { formLine, formLineIndex, clearFormLine, formLineErrors: errors, setFormLinePartial } = useScrStore();
  // console.log(`errors`, errors);

  const isEdit = formLineIndex !== null;

  return (
    <>
      <Table size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <colgroup>
          <col style={{ width: '2%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '8%' }} />
          {/* 5 */}
          <col style={{ width: '5%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          {/* 10 */}
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '3%' }} />
          {/* 15 */}
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell></TableCell>
            <TableCell align="center">C/R/N/D</TableCell>
            <TableCell>Operator</TableCell>
            <TableCell>FltNo</TableCell>
            <TableCell sx={{ minWidth: '10rem' }}>Date Range</TableCell>
            {/* 5 */}
            <TableCell>DOOP</TableCell>
            <TableCell align="center">Seats</TableCell>
            <TableCell align="center">A/C</TableCell>
            <TableCell align="center">Origin</TableCell>
            <TableCell align="center">Prev</TableCell>
            {/* 10 */}
            <TableCell align="center">Time</TableCell>
            <TableCell align="center">O/N</TableCell>
            <TableCell align="center">Next</TableCell>
            <TableCell align="center">Dest</TableCell>
            <TableCell align="center">St</TableCell>
            {/* 15 */}
            <TableCell align="center">Frq</TableCell>
            <TableCell align="center">Acreg</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell component="th" scope="row">
              Arr
            </TableCell>
            {/* Action */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs.action.label}
                name={inputs.action.storeKey}
                select
                value={formLine?.action || ''}
                onChange={getOnChange('action')}
                error={!!errors?.[inputs.action.storeKey]}
                helperText={showError(errors?.[inputs.action.storeKey])}
              >
                {inputs.action.options?.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </TableCell>
            {/* Operator */}
            <TableCell>
              <TextField
                fullWidth
                label={inputs?.operator1?.label}
                name={inputs?.operator1?.storeKey}
                value={formLine?.operator1 || ''}
                onChange={getOnChange('operator1')}
                error={!!errors?.[inputs.operator1.storeKey]}
                helperText={showError(errors?.[inputs.operator1.storeKey])}
              />
            </TableCell>
            {/* Flight Number */}
            <TableCell>
              <TextField
                label={inputs?.flightNumber1?.label}
                name={inputs?.flightNumber1?.storeKey}
                value={formLine?.flightNumber1 || ''}
                onChange={getOnChange('flightNumber1')}
                error={!!errors?.[inputs.flightNumber1.storeKey]}
                helperText={showError(errors?.[inputs.flightNumber1.storeKey])}
              />
            </TableCell>
            {/* Date Range */}
            <TableCell rowSpan={2}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  label={inputs?.dateFrom?.label}
                  name={inputs?.dateFrom?.storeKey}
                  value={formLine?.dateFrom || ''}
                  onChange={getOnChange('dateFrom')}
                  error={!!errors?.[inputs.dateFrom.storeKey]}
                  helperText={showError(errors?.[inputs.dateFrom.storeKey])}
                  sx={{ maxWidth: '5rem' }}
                />
                <TextField
                  label={inputs?.dateTo?.label}
                  name={inputs?.dateTo?.storeKey}
                  value={formLine?.dateTo || ''}
                  onChange={getOnChange('dateTo')}
                  error={!!errors?.[inputs.dateTo.storeKey]}
                  helperText={showError(errors?.[inputs.dateTo.storeKey])}
                  sx={{ maxWidth: '5rem' }}
                />
              </Box>
            </TableCell>
            {/* DOOP */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.doop?.label}
                name={inputs?.doop?.storeKey}
                value={formLine?.doop || ''}
                onChange={getOnChange('doop')}
                onBlur={(e) => {
                  // Convert DOOP shorthand to full format on blur
                  setFormLinePartial({ doop: formatDoop(e.target.value) });
                }}
                error={!!errors?.[inputs.doop.storeKey]}
                helperText={showError(errors?.[inputs.doop.storeKey])}
              />
            </TableCell>
            {/* Seats */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.seats?.label}
                name={inputs?.seats?.storeKey}
                value={formLine?.seats || ''}
                onChange={getOnChange('seats')}
                error={!!errors?.[inputs.seats.storeKey]}
                helperText={showError(errors?.[inputs.seats.storeKey])}
              />
            </TableCell>
            {/* Aircraft Type */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.aircraftType?.label}
                name={inputs?.aircraftType?.storeKey}
                value={formLine?.aircraftType || ''}
                onChange={getOnChange('aircraftType')}
                error={!!errors?.[inputs.aircraftType.storeKey]}
                helperText={showError(errors?.[inputs.aircraftType.storeKey])}
              />
            </TableCell>
            {/* Origin */}
            <TableCell>
              <TextField
                label={inputs?.origin?.label}
                name={inputs?.origin?.storeKey}
                value={formLine?.origin || ''}
                onChange={getOnChange('origin')}
                error={!!errors?.[inputs.origin.storeKey]}
                helperText={showError(errors?.[inputs.origin.storeKey])}
              />
            </TableCell>
            {/* Previous */}
            <TableCell>
              <TextField
                label={inputs?.previous?.label}
                name={inputs?.previous?.storeKey}
                value={formLine?.previous || ''}
                onChange={getOnChange('previous')}
                error={!!errors?.[inputs.previous.storeKey]}
                helperText={showError(errors?.[inputs.previous.storeKey])}
              />
            </TableCell>
            {/* Time 1 */}
            <TableCell>
              <TextField
                label={inputs?.time1?.label}
                name={inputs?.time1?.storeKey}
                value={formLine?.time1 || ''}
                onChange={getOnChange('time1')}
                error={!!errors?.[inputs.time1.storeKey]}
                helperText={showError(errors?.[inputs.time1.storeKey])}
              />
            </TableCell>
            {/* O/N */}
            <TableCell>{/* O/N */}</TableCell>
            {/* Next */}
            <TableCell>{/* Next */}</TableCell>
            {/* Destination */}
            <TableCell>{/* Dest */}</TableCell>
            {/* Service Type*/}
            <TableCell>
              <TextField
                label={inputs?.st1?.label}
                name={inputs?.st1?.storeKey}
                value={formLine?.st1 || ''}
                onChange={getOnChange('st1')}
                select
                error={!!errors?.[inputs.st1.storeKey]}
                helperText={showError(errors?.[inputs.st1.storeKey])}
              >
                {inputs?.st1?.options?.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </TableCell>
            {/* Frequency */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.frequency?.label}
                name={inputs?.frequency?.storeKey}
                value={formLine?.frequency || ''}
                onChange={getOnChange('frequency')}
                error={!!errors?.[inputs.frequency.storeKey]}
                helperText={showError(errors?.[inputs.frequency.storeKey])}
              />
            </TableCell>
            {/* Aircraft Registration */}
            <TableCell rowSpan={2}>
              <TextField
                label={inputs?.aircraftRegistration?.label}
                name={inputs?.aircraftRegistration?.storeKey}
                value={formLine?.aircraftRegistration || ''}
                onChange={getOnChange('aircraftRegistration')}
                error={!!errors?.[inputs.aircraftRegistration.storeKey]}
                helperText={showError(errors?.[inputs.aircraftRegistration.storeKey])}
              />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell component="th" scope="row">
              Dep
            </TableCell>
            {/* Action */}
            {/* rowSpan={2} */}
            {/* Operator */}
            <TableCell>
              <TextField
                label={inputs?.operator2?.label}
                name={inputs?.operator2?.storeKey}
                value={formLine?.operator2 || ''}
                onChange={getOnChange('operator2')}
                error={!!errors?.[inputs.operator2.storeKey]}
                helperText={showError(errors?.[inputs.operator2.storeKey])}
              />
            </TableCell>
            {/* Flight Number */}
            <TableCell>
              <TextField
                label={inputs?.flightNumber2?.label}
                name={inputs?.flightNumber2?.storeKey}
                value={formLine?.flightNumber2 || ''}
                onChange={getOnChange('flightNumber2')}
                error={!!errors?.[inputs.flightNumber2.storeKey]}
                helperText={showError(errors?.[inputs.flightNumber2.storeKey])}
              />
            </TableCell>
            {/* Date Range */}
            {/* rowSpan={2} */}
            {/* DOOP */}
            {/* rowSpan={2} */}
            {/* Seats */}
            {/* rowSpan={2} */}
            {/* Aircraft Type */}
            {/* rowSpan={2} */}
            {/* Origin */}
            <TableCell>{/* Origin */}</TableCell>
            {/* Previous */}
            <TableCell>{/* Prev */}</TableCell>
            {/* Time 2 */}
            <TableCell>
              <TextField
                label={inputs?.time2?.label}
                name={inputs?.time2?.storeKey}
                value={formLine?.time2 || ''}
                onChange={getOnChange('time2')}
                error={!!errors?.[inputs.time2.storeKey]}
                helperText={showError(errors?.[inputs.time2.storeKey])}
              />
            </TableCell>
            {/* O/N */}
            <TableCell>
              {/* O/N */}
              <TextField
                label={inputs?.on?.label}
                name={inputs?.on?.storeKey}
                value={formLine?.on || ''}
                onChange={getOnChange('on')}
                error={!!errors?.[inputs.on.storeKey]}
                helperText={showError(errors?.[inputs.on.storeKey])}
              />
            </TableCell>
            {/* Next */}
            <TableCell>
              {/* Next */}
              <TextField
                label={inputs?.next?.label}
                name={inputs?.next?.storeKey}
                value={formLine?.next || ''}
                onChange={getOnChange('next')}
                error={!!errors?.[inputs.next.storeKey]}
                helperText={showError(errors?.[inputs.next.storeKey])}
              />
            </TableCell>
            {/* Destination */}
            <TableCell>
              {/* Dest */}
              <TextField
                label={inputs?.destination?.label}
                name={inputs?.destination?.storeKey}
                value={formLine?.destination || ''}
                onChange={getOnChange('destination')}
                error={!!errors?.[inputs.destination.storeKey]}
                helperText={showError(errors?.[inputs.destination.storeKey])}
              />
            </TableCell>
            {/* Service Type*/}
            <TableCell>
              <TextField
                label={inputs?.st2?.label}
                name={inputs?.st2?.storeKey}
                value={formLine?.st2 || ''}
                onChange={getOnChange('st2')}
                select
                error={!!errors?.[inputs.st2.storeKey]}
                helperText={showError(errors?.[inputs.st2.storeKey])}
              >
                {inputs?.st2?.options?.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </TextField>
            </TableCell>
            {/* Frequency */}
            {/* rowSpan={2} */}
            {/* Aircraft Registration */}
            {/* rowSpan={2} */}
          </TableRow>
        </TableBody>
      </Table>

      {DEBUG && (
        <TableFormInnerBox>
          {mockLines.map((line, i) => (
            <MockButton key={i} onClick={() => setFormLinePartial({ ...line })}>
              {i}
            </MockButton>
          ))}
        </TableFormInnerBox>
      )}

      <TableFormInnerBox>
        <FormButton variant="contained" size="small" onClick={handleAddLine}>
          {isEdit ? `Update Line` : `Add Line`}
        </FormButton>
        <FormButton variant="contained" color="warning" size="small" onClick={() => clearFormLine()}>
          Clear
        </FormButton>
      </TableFormInnerBox>
    </>
  );
};
