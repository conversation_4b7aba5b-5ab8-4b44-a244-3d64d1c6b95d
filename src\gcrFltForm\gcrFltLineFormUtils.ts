import { type BaseFormStore } from '@src/formStore/formStoreTypes';
import type { FormConfig } from '@src/typesGlobal';
import { gcrFltLineFormConfig } from './gcrFltLineFormConfig';
import type { GcrFltLine, GcrFltMessage } from './gcrFltTypes';

export type GcrFltLineInputs = FormConfig<GcrFltLine>['inputs'];
export type GcrFltUpdateLine = BaseFormStore<GcrFltLine, GcrFltMessage>['setFormLinePartial'];
export type GcrFltLineSetFocusKey = (key: keyof GcrFltLine | null) => void;

/**
 * 'getGcrFltLineOnChange' will given a field key and a function to update the line,
 * return a function that takes an event and updates the store for that field.
 */
export const getGcrFltLineOnChange =
  (key: keyof GcrFltLine, setFormLinePartial: GcrFltUpdateLine) => (e: React.ChangeEvent<HTMLInputElement>) => {
    let value: GcrFltLine[keyof GcrFltLine] = e.target.value;
    // Check if this field should be a number type
    if (gcrFltLineFormConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
      value = Number(e.target.value);
    }
    if (gcrFltLineFormConfig.inputs[key]?.toUpperCase) {
      value = e.target.value?.toUpperCase();
    }
    setFormLinePartial({ [key]: value });
  };

/** For an input, get the next input in the tab order  */
const getNextTabInput = (inputs: GcrFltLineInputs, currentKey: keyof GcrFltLine, reverse: boolean = false) => {
  const currentIndex = inputs[currentKey].tabOrder || 0;
  const nextIndex = reverse ? currentIndex - 1 : currentIndex + 1;
  const nextInput = Object.values(inputs).find((input) => input.tabOrder === nextIndex);
  return nextInput;
};

/**
 * For an input, get the onKey handler that handles tabbing and character entry.
 * Pressing tab will focus on the next input in the tab order,
 * and shift-tab will focus on the previous input in the tab order.
 * If the input is a select, it will also handle character entry to select an option.
 * */
export const getGcrFltOnKeyHandler =
  (
    inputs: GcrFltLineInputs,
    currentInputKey: keyof GcrFltLine,
    updateLine: GcrFltUpdateLine,
    setFocusKey: GcrFltLineSetFocusKey,
  ) =>
  (e: React.KeyboardEvent<HTMLElement>) => {
    // console.log(cloneDeep(e));
    const thisInput = inputs[currentInputKey];
    // console.log(`Character key: ${e.key}`);

    // If a character key is pressed, update state:
    if (thisInput.type === 'select' && e.key.length === 1 && !e.ctrlKey && !e.metaKey && !e.altKey) {
      const match = thisInput.options?.find((option) => option.toLowerCase() === e.key.toLowerCase());
      // console.log(`match`, match);
      if (match) updateLine?.({ [currentInputKey]: match });
    }

    // If tab is pressed, focus on next input
    if (e.key === 'Tab') {
      e.preventDefault();
      // e.stopPropagation();
      const nextInput = getNextTabInput(inputs, currentInputKey, e.shiftKey);
      setFocusKey(nextInput?.storeKey || null);
      if (nextInput) {
        const nextElement = document.querySelector(`[name="${nextInput.storeKey}"]`);
        // console.log(`nextElement`, nextElement);
        if (nextElement) (nextElement as HTMLInputElement)?.focus();
        nextElement?.setAttribute('aria-hidden', 'false');
      }
    }
  };
