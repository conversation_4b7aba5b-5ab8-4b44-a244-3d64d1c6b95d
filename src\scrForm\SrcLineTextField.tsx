import { MenuItem, TextField } from '@mui/material';
import { ErrorLabel } from '@src/components/FormComponents';
import type { Maybe } from '@src/typesGlobal';
import { scrLineFormConfig } from './scrLineFormConfig';
import { getScrOnKeyHandler, getSrcLineOnChange } from './scrLineFormTableUtils';
import type { ScrLine } from './scrTypes';
import { useScrStore } from './useScrStore';

const showError = (error: Maybe<string>) => <ErrorLabel error={error} />;

type SrcLineTextFieldProps = React.ComponentProps<typeof TextField> & {
  currentInputKey: keyof ScrLine;
};

export const ScrLineTextField: React.FC<SrcLineTextFieldProps> = (props) => {
  const { currentInputKey, ...rest } = props;
  const inputs = scrLineFormConfig.inputs;
  const thisInput = inputs[currentInputKey];

  const {
    formLine,
    formLineErrors: errors,
    setFormLinePartial: updateLine,
    formLineFocusKey,
    setFormLineFocusKey,
  } = useScrStore();
  // console.log(`errors`, errors);

  // Check if this input has options (making it a select field)
  const isSelect = thisInput.type === 'select';

  return (
    <TextField
      label={thisInput.label}
      name={thisInput.storeKey}
      select={isSelect}
      value={formLine?.[currentInputKey] || ''}
      onChange={getSrcLineOnChange(currentInputKey, updateLine)}
      onKeyDown={getScrOnKeyHandler(inputs, currentInputKey, updateLine, setFormLineFocusKey)}
      focused={formLineFocusKey === currentInputKey}
      helperText={showError(errors?.[currentInputKey])}
      error={!!errors?.[currentInputKey]}
      {...rest}
    >
      {isSelect &&
        thisInput.options?.map((option) => (
          <MenuItem key={option} value={option}>
            {option}
          </MenuItem>
        ))}
    </TextField>
  );
};
