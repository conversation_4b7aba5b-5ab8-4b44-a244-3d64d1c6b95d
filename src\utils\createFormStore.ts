import type { BaseFormStore } from '@src/typesGlobal';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

/**
 * Configuration for creating a form store
 */
interface FormStoreConfig<TLine, TMessage> {
  /** Empty line object for form initialization */
  emptyLine: Partial<TLine>;
  /** Empty message object for form initialization */
  emptyMessage: Partial<TMessage>;
}

/**
 * Factory function to create a Zustand store with immer middleware
 * that implements the BaseFormStore interface.
 *
 * @param config Configuration object containing empty line and message objects
 * @returns A Zustand store hook
 */
export const createFormStore = <TLine, TMessage>(config: FormStoreConfig<TLine, TMessage>) => {
  const { emptyLine, emptyMessage } = config;
  // console.log(`createFormStore`);

  type StoreState = BaseFormStore<TLine, TMessage>;

  return create<StoreState>()(
    immer((set) => ({
      // Message:
      message: null,
      setMessage: (message) =>
        set((state) => {
          (state as Record<string, unknown>).message = message;
        }),
      clearMessage: () =>
        set((state) => {
          state.message = null;
        }),

      // Message form:
      formMessage: emptyMessage,
      setFormMessagePartial: (updates) =>
        set((state) => {
          Object.assign(state.formMessage || {}, updates);
        }),
      clearFormMessage: () =>
        set((state) => {
          (state as Record<string, unknown>).formMessage = emptyMessage;
        }),

      // Form message error map:
      formMessageErrors: null,
      setFormMessageErrors: (errors) =>
        set((state) => {
          state.formMessageErrors = errors;
        }),
      clearFormMessageErrors: () =>
        set((state) => {
          state.formMessageErrors = null;
        }),

      // Lines:
      lines: [],
      addLine: (line) =>
        set((state) => {
          (state.lines as unknown[]).push(line);
        }),
      updateLine: (updates) =>
        set((state) => {
          if (state.formLineIndex === null) return;
          Object.assign(state.lines[state.formLineIndex] as Record<string, unknown>, updates);
        }),
      deleteLine: (index) =>
        set((state) => {
          state.lines.splice(index, 1);
        }),
      clearAllLines: () =>
        set((state) => {
          state.lines = [];
        }),

      // Edit line:
      formLine: emptyLine,
      formLineIndex: null,
      setFormLine: (index = null) =>
        set((state) => {
          state.formLineIndex = index;
          if (index === null) return;
          state.formLine = state.lines[index || 0];
        }),
      setFormLinePartial: (updates) =>
        set((state) => {
          Object.assign(state.formLine || {}, updates);
        }),
      clearFormLine: () =>
        set((state) => {
          (state as Record<string, unknown>).formLine = emptyLine;
          state.formLineIndex = null;
          state.formLineErrors = null;
        }),

      // Line error state:
      formLineErrors: null,
      setFormLineErrors: (errors) =>
        set((state) => {
          state.formLineErrors = errors;
        }),
      clearFormLineErrors: () =>
        set((state) => {
          state.formLineErrors = null;
        }),
    })),
  );
};

/**
 * Helper function to create the standard empty message with today's date
 * in the format used by the application (DDMMM)
 */
export const createEmptyMessageWithDate = (): { date: string } => {
  const today = new Date();
  const month = today.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
  const day = today.getDate().toString().padStart(2, '0');
  const todayFormatted = `${day}${month}`;
  return { date: todayFormatted };
};
