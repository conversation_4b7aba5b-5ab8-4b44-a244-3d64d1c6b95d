import { isNil } from 'lodash';
import type { ScrLine, ScrMessage } from './scrTypes';

const val2str = <T = unknown>(val: T): string => (isNil(val) ? '' : String(val));

export const formatScrLine = (line: ScrLine): string => {
  const action = val2str(line.action);
  const operator1 = val2str(line.operator1);
  const flightNumber1 = val2str(line.flightNumber1?.padStart(4, '0'));
  const operator2 = val2str(line.operator2);
  const flightNumber2 = val2str(line.flightNumber2?.padStart(4, '0'));
  const dateFrom = val2str(line.dateFrom);
  const dateTo = val2str(line.dateTo);
  const doop = val2str(line.doop);
  const seats = val2str(line.seats?.toString().padStart(3, '0'));
  const aircraftType = val2str(line.aircraftType);
  const origin = val2str(line.origin);
  const previous = val2str(line.previous);
  const time1 = val2str(line.time1);
  const time2 = val2str(line.time2);
  const on = line?.on === 0 ? '' : val2str(line?.on);
  const next = val2str(line.next);
  const destination = val2str(line.destination);
  const st1 = val2str(line.st1);
  const st2 = val2str(line.st2);
  const frequency = val2str(line.frequency);
  const aircraftRegistration = `/ ${val2str(line?.aircraftRegistration)}/`;

  // let intro = operator1 ? `${action}${operator1}${flightNumber1}` : `${action}${operator2}${flightNumber2}`;
  // operator1 && operator2 && (intro += ` ${operator2}${flightNumber2}`);
  let result =
    // intro +
    `${action}${operator1}${flightNumber1}` +
    ` ${operator2}${flightNumber2}` +
    ` ${dateFrom}${dateTo} ${doop} ${seats}${aircraftType}` +
    ` ${origin}${previous}${time1} ${time2}${on}${next}${destination}` +
    `${st1}${st2}${frequency} ${aircraftRegistration}`;

  // Remove double spaces
  result = result.replace(/ {2,}/g, ' ').trim();

  // console.log({ result });
  return result;
};

export const formatScrMsgHeader = (msg: ScrMessage): string => {
  const creator = msg.creator ? `\n/${msg.creator}` : '';
  return `SCR${creator}\n${msg.season}\n${msg.date}\n${msg.airport}`;
};

export const formatScrMsgFooter = (msg: ScrMessage): string => {
  return `SI ${msg.si}\nGI ${msg.gi}`;
};

export const formatScrMessage = (msg: ScrMessage, lines: ScrLine[]): string => {
  const header = formatScrMsgHeader(msg);
  const linesAsStrings = lines.map(formatScrLine);
  const footer = formatScrMsgFooter(msg);
  return [header, ...linesAsStrings, footer].join('\n');
};
