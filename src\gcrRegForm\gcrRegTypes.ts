import type { ScrAction, ServiceType } from '@src/validation/validationSchemas';

/** SCR line shared fields */
export type GcrRegSharedLineFields = {
  action: ScrAction;
  date: string;
  seats: number;
  aircraftType: string;
  aircraftRegistration?: string;
};
/** SCR line arrival fields */
export type GcrRegArrLineFields = {
  origin: string;
  previous?: string;
  timeArr: string;
  stArr: ServiceType;
};
/** SCR line departure fields */
export type GcrRegDepLineFields = {
  timeDep: string;
  on?: number;
  next?: string;
  destination: string;
  stDep: ServiceType;
};

/** SCR arrival line type */
export type GcrRegArrLine = GcrRegArrLineFields & GcrRegSharedLineFields;
/** SCR departure line type */
export type GcrRegDepLine = GcrRegDepLineFields & GcrRegSharedLineFields;

/** SCR line form validation */
export type GcrRegLine = GcrRegSharedLineFields & GcrRegArrLineFields & GcrRegDepLineFields;
/** SCR line form input */
export type GcrRegLineFormInput = Partial<GcrRegLine>;

/** For message form validation */
export type GcrRegMessage = {
  /** Coordinated airport IATA code (e.g. CPH) */
  airport: string;
  /* Scheduled information */
  si?: string;
  /** General information */
  gi?: string;
};

/** For full message validation */
export type GcrRegFullMessage = {
  message: GcrRegMessage;
  lines: GcrRegLine[];
};
