import type { ScrLine, ScrMessage } from './types';

export type FormInputEvent = React.ChangeEvent<HTMLInputElement>;
export type FormOnChangeHandler = (e: FormInputEvent) => void;

export type FormScrLineInput = {
  storeKey: keyof ScrLine;
  label: string;
  type: 'text' | 'number' | 'select';
  options?: readonly string[];
  toUpperCase?: boolean;
};

export type FormSrcLineConfig = {
  inputs: {
    [K in keyof ScrLine]-?: FormScrLineInput;
  };
};

export type FormScrLineMethods = {
  handleChange: (key: keyof ScrLine, value: ScrLine[keyof ScrLine]) => void;
  handleAddLine: () => void;
};

export type FormScrMsgInput = {
  storeKey: keyof ScrMessage;
  label: string;
  type: 'text';
  toUpperCase?: boolean;
};

export type FormSrcMsgConfig = {
  inputs: {
    [K in keyof ScrMessage]-?: FormScrMsgInput;
  };
};

export type FormSrcMsgMethods = {
  handleChange: (key: keyof ScrMessage, value: ScrMessage[keyof ScrMessage]) => void;
  handleAddMessage: () => void;
};
