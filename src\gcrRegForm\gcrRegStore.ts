import { createEmptyMessageWithDate, createFormStore } from '@src/formStore/createFormStore';
import type { GcrRegLine, GcrRegLineFormInput, GcrRegMessage } from './gcrRegTypes';

const emptyLine: GcrRegLineFormInput = { action: 'N' };
const emptyMessage: Partial<GcrRegMessage> = createEmptyMessageWithDate();

export const useGcrRegStore = createFormStore<GcrRegLine, GcrRegMessage>({
  emptyLine,
  emptyMessage,
});
