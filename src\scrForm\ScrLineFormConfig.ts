import type { FormConfig } from '@src/typesGlobal';
import { lineActionValues, serviceTypeValues } from '@src/validation/validationSchemas';
import { type ScrLine } from './scrTypes';

export const scrLineFormConfig: FormConfig<ScrLine> = {
  inputs: {
    action: {
      storeKey: 'action',
      label: 'Action',
      type: 'select',
      options: lineActionValues,
      tabOrder: 1,
    },
    operatorArr: {
      storeKey: 'operatorArr',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 2,
    },
    flightNumberArr: {
      storeKey: 'flightNumberArr',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 3,
    },
    operatorDep: {
      storeKey: 'operatorDep',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 4,
    },
    flightNumberDep: {
      storeKey: 'flightNumberDep',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 5,
    },
    dateFrom: {
      storeKey: 'dateFrom',
      label: 'From',
      type: 'text',
      toUpperCase: true,
      tabOrder: 6,
    },
    dateTo: {
      storeKey: 'dateTo',
      label: 'To',
      type: 'text',
      toUpperCase: true,
      tabOrder: 7,
    },
    doop: {
      storeKey: 'doop',
      label: '',
      type: 'text',
      tabOrder: 8,
    },
    seats: {
      storeKey: 'seats',
      label: '',
      type: 'number',
      tabOrder: 9,
    },
    aircraftType: {
      storeKey: 'aircraftType',
      label: '',
      type: 'text',
      tabOrder: 10,
    },
    origin: {
      storeKey: 'origin',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 11,
    },
    previous: {
      storeKey: 'previous',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 12,
    },
    timeArr: {
      storeKey: 'timeArr',
      label: '',
      type: 'text',
      tabOrder: 13,
    },
    timeDep: {
      storeKey: 'timeDep',
      label: '',
      type: 'text',
      tabOrder: 14,
    },
    on: {
      storeKey: 'on',
      label: '',
      type: 'number',
      tabOrder: 15,
    },
    next: {
      storeKey: 'next',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 16,
    },
    destination: {
      storeKey: 'destination',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 17,
    },
    stArr: {
      storeKey: 'stArr',
      label: '',
      type: 'select',
      options: serviceTypeValues,
      tabOrder: 18,
    },
    stDep: {
      storeKey: 'stDep',
      label: '',
      type: 'select',
      options: serviceTypeValues,
      tabOrder: 19,
    },
    frequency: {
      storeKey: 'frequency',
      type: 'text',
      label: '',
      toUpperCase: true,
      tabOrder: 20,
    },
    aircraftRegistration: {
      storeKey: 'aircraftRegistration',
      type: 'text',
      label: '',
      toUpperCase: true,
      tabOrder: 21,
    },
  },
};
