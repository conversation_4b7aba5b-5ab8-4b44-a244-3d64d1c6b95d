import type { FormConfig } from '@src/typesGlobal';
import { zodErrorMap } from '@src/utils/utils';
import { stripEmptyStringsFromInput } from '@src/validation/utils';
import { ScrActionValues, serviceTypeValues } from '@src/validation/validationSchemas';
import { useCallback, useMemo } from 'react';
import { GcrRegLineFormTableRenderer } from './GcrRegLineFormTableRenderer';
import { useGcrRegStore } from './gcrRegStore';
import { type GcrRegLine } from './gcrRegTypes';
import { gcrRegLineSchema } from './gcrRegValidation';

/**
 * The 'GcrFltLineFormContainer' creates config and handlers for the line form
 * and passes them to the 'GcrFltLineFormTableRenderer'
 */
export const GcrRegLineFormContainer: React.FC = () => {
  const {
    addLine,
    updateLine,
    formLine,
    setFormLinePartial,
    formLineIndex,
    clearFormLine,
    setFormLineErrors,
    clearFormLineErrors,
  } = useGcrRegStore();

  const isEdit = formLineIndex !== null;

  /**
   * 'handleAddLine' will validate the formLine and add it to the store.
   * Memoize it to prevent unnecessary re-renders.
   */
  const handleAddLine = useCallback(() => {
    const cleanedFormLine = stripEmptyStringsFromInput(formLine);
    // console.log(`cleanedFormLine;`, JSON.stringify(cleanedFormLine, null, 2));
    const parsed = gcrRegLineSchema.safeParse(cleanedFormLine);
    if (!parsed.success) {
      const errorMap = zodErrorMap(parsed.error);
      // console.log(`errorMap`, errorMap);
      setFormLineErrors(errorMap);
      return;
    }
    clearFormLineErrors();
    isEdit ? updateLine(parsed.data) : addLine(parsed.data);
    clearFormLine();
  }, [addLine, clearFormLine, clearFormLineErrors, formLine, isEdit, setFormLineErrors, updateLine]);

  /**
   * 'formConfig' is memoized to prevent unnecessary re-renders.
   * It contains the configuration for the form inputs.
   */
  const formConfig: FormConfig<GcrRegLine> = useMemo(
    () => ({
      inputs: {
        action: {
          storeKey: 'action',
          label: 'Action',
          type: 'select',
          options: ScrActionValues,
        },
        date: {
          storeKey: 'date',
          label: '',
          type: 'text',
          toUpperCase: true,
        },
        seats: {
          storeKey: 'seats',
          label: '',
          type: 'number',
        },
        aircraftType: {
          storeKey: 'aircraftType',
          label: '',
          type: 'text',
        },
        origin: {
          storeKey: 'origin',
          label: '',
          type: 'text',
          toUpperCase: true,
        },
        previous: {
          storeKey: 'previous',
          label: '',
          type: 'text',
          toUpperCase: true,
        },
        timeArr: {
          storeKey: 'timeArr',
          label: '',
          type: 'text',
        },
        timeDep: {
          storeKey: 'timeDep',
          label: '',
          type: 'text',
        },
        on: {
          storeKey: 'on',
          label: '',
          type: 'number',
        },
        next: {
          storeKey: 'next',
          label: '',
          type: 'text',
          toUpperCase: true,
        },
        destination: {
          storeKey: 'destination',
          label: '',
          type: 'text',
          toUpperCase: true,
        },
        stArr: {
          storeKey: 'stArr',
          label: '',
          type: 'select',
          options: serviceTypeValues,
        },
        stDep: {
          storeKey: 'stDep',
          label: '',
          type: 'select',
          options: serviceTypeValues,
        },
        aircraftRegistration: {
          storeKey: 'aircraftRegistration',
          type: 'text',
          label: '',
          toUpperCase: true,
        },
      },
    }),
    [],
  );

  /**
   * 'getOnChange' will given a field key, return a function that takes an event
   * and updates the store for that field.
   * Memoize it to prevent unnecessary re-renders.
   */
  const getOnChange = useCallback(
    (key: keyof GcrRegLine) => (e: React.ChangeEvent<HTMLInputElement>) => {
      // console.log(`getOnChange`, key, e.target.value);
      let value: GcrRegLine[keyof GcrRegLine] = e.target.value;
      // Check if this field should be a number type
      if (formConfig.inputs[key]?.type === 'number' && e.target.value !== '') {
        value = Number(e.target.value);
      }
      if (formConfig.inputs[key]?.toUpperCase) {
        value = e.target.value?.toUpperCase();
      }
      setFormLinePartial({ [key]: value });
    },
    [formConfig.inputs, setFormLinePartial],
  );

  return (
    <GcrRegLineFormTableRenderer formConfig={formConfig} getOnChange={getOnChange} handleAddLine={handleAddLine} />
  );
};
