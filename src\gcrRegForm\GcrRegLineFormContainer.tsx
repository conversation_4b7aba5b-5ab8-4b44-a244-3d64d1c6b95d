import { zodErrorMap } from '@src/utils/utils';
import { stripEmptyStringsFromInput } from '@src/validation/utils';
import { useCallback } from 'react';
import { GcrRegLineFormTableRenderer } from './GcrRegLineFormTableRenderer';
import { useGcrRegStore } from './gcrRegStore';
import { gcrRegLineSchema } from './gcrRegValidation';

/**
 * The 'GcrFltLineFormContainer' creates config and handlers for the line form
 * and passes them to the 'GcrFltLineFormTableRenderer'
 */
export const GcrRegLineFormContainer: React.FC = () => {
  const { addLine, updateLine, formLine, formLineIndex, clearFormLine, setFormLineErrors, clearFormLineErrors } =
    useGcrRegStore();

  const isEdit = formLineIndex !== null;

  /**
   * 'handleAddLine' will validate the formLine and add it to the store.
   * Memoize it to prevent unnecessary re-renders.
   */
  const handleAddLine = useCallback(() => {
    const cleanedFormLine = stripEmptyStringsFromInput(formLine);
    // console.log(`cleanedFormLine;`, JSON.stringify(cleanedFormLine, null, 2));
    const parsed = gcrRegLineSchema.safeParse(cleanedFormLine);
    if (!parsed.success) {
      const errorMap = zodErrorMap(parsed.error);
      // console.log(`errorMap`, errorMap);
      setFormLineErrors(errorMap);
      return;
    }
    clearFormLineErrors();
    isEdit ? updateLine(parsed.data) : addLine(parsed.data);
    clearFormLine();
  }, [addLine, clearFormLine, clearFormLineErrors, formLine, isEdit, setFormLineErrors, updateLine]);

  return <GcrRegLineFormTableRenderer handleAddLine={handleAddLine} />;
};
