import {
  actionSchema,
  aircraftRegSchema,
  dateSchema,
  doopSchemaFull,
  flightNumberSchema,
  operatorSchema,
  schema3charsNotBlank,
  schema3charsOrBlank,
  serviceTypeSchema,
  timeSchema,
} from '@src/validation/validationSchemas';
import { z } from 'zod';
import {
  type ScrArrLineFields,
  type ScrDepLineFields,
  type ScrLine,
  type ScrMessage,
  type ScrSharedLineFields,
} from './scrTypes';

const scrSharedFields = {
  action: actionSchema.refine((val) => val !== undefined, { message: 'Required' }),
  // dateFrom required
  dateFrom: dateSchema,
  // dateTo required
  dateTo: dateSchema,
  // doop required
  doop: doopSchemaFull,
  // seats required
  seats: z.number().int().positive().max(999, { message: '1-999' }),
  // aircraftType required
  aircraftType: z.string().length(3, { message: '3 characters' }),
  // frequency optional
  frequency: z
    .string()
    .regex(/^(|2)$/, { message: '2 or blank' })
    .optional(),
  // aircraftRegistration optional
  aircraftRegistration: aircraftRegSchema.optional(),
};

const scrArrFields = {
  // operator1
  operatorArr: operatorSchema,
  // flightNumber1
  flightNumberArr: flightNumberSchema,
  // origin required
  origin: schema3charsNotBlank,
  // optional
  previous: schema3charsOrBlank.optional(),
  // time1
  timeArr: timeSchema,
  // Service type 1
  stArr: serviceTypeSchema,
};

const scrDepFields = {
  // operator2
  operatorDep: operatorSchema,
  // flightNumber2
  flightNumberDep: flightNumberSchema,
  // time2
  timeDep: timeSchema,
  // ON is for over night
  on: z.number().int().nonnegative().lte(9, { message: '0-9 or blank' }).optional(),
  // optional
  next: schema3charsOrBlank.optional(),
  // required
  destination: schema3charsNotBlank,
  // Service type 2
  stDep: serviceTypeSchema,
};

export const scrSharedSchema = z.object(scrSharedFields) satisfies z.ZodType<ScrSharedLineFields>;
export const scrArrSchema = z.object(scrArrFields) satisfies z.ZodType<ScrArrLineFields>;
export const scrDepSchema = z.object(scrDepFields) satisfies z.ZodType<ScrDepLineFields>;

export const StructuralPrefix = 'STRUCTURAL_';

export const scrLineSchema = z
  // Join the schemas
  .object({
    ...scrArrSchema.shape,
    ...scrDepSchema.shape,
    ...scrSharedSchema.shape,
  })
  // Losen up to make sure superRefine will run:
  .partial()
  // Apply logic to check if arrival, departure or both are filled:
  .superRefine((data, ctx) => {
    // console.count('superRefine');
    // console.log(`superRefine`, { data, ctx });
    const hasArrival = Object.keys(scrArrSchema.shape).some((key) => !!data[key as keyof typeof data]);
    // console.log(`hasArrival`, hasArrival);
    const hasDeparture = Object.keys(scrDepSchema.shape).some((key) => !!data[key as keyof typeof data]);
    // console.log(`hasDeparture`, hasDeparture);

    if (!hasArrival && !hasDeparture) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Arrival or Departure must be filled',
        path: [`${StructuralPrefix}Arrival or Departure`],
      });
      return;
    }

    if (hasArrival) {
      const result = scrArrSchema.safeParse(data);
      if (!result.success) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'When one arrival field is filled, all are required',
          path: [`${StructuralPrefix}Arrival`],
        });
        result.error.issues.forEach((issue) => ctx.addIssue(issue));
      }
    }

    if (hasDeparture) {
      const result = scrDepSchema.safeParse(data);
      if (!result.success) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'When one departure field is filled, all are required',
          path: [`${StructuralPrefix}Departure`],
        });
        result.error.issues.forEach((issue) => ctx.addIssue(issue));
      }
    }

    // Check shared fields in either case
    const sharedResult = scrSharedSchema.safeParse(data);
    if (!sharedResult.success) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Shared fields are required, except frequency and aircraftRegistration',
        path: [`${StructuralPrefix}Shared`],
      });
      sharedResult.error.issues.forEach((issue) => ctx.addIssue(issue));
    }
  })
  .transform((data) => data as ScrLine);
// satisfies z.ZodType<ScrLineFormInput>;
// }) satisfies z.ZodType<ScrLine>;

// ScrMessage schema:
export const scrMessageSchema = z.object({
  season: z.string().regex(/^[SW]\d{2}$/, { message: 'W## / S## format' }),
  date: dateSchema,
  airport: schema3charsOrBlank,
  creator: z.string().email({ message: 'Valid email or blank' }).or(z.string().regex(/^$/)),
  si: z.string().optional(),
  gi: z.string().optional(),
}) satisfies z.ZodType<ScrMessage>;
