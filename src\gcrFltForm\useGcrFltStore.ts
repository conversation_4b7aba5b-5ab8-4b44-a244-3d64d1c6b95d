import { createEmptyMessageWithDate, createFormStore } from '@src/formStore/createFormStore';
import type { GcrFltLine, GcrFltLineFormInput, GcrFltMessage } from './gcrFltTypes';

const emptyLine: GcrFltLineFormInput = { action: 'N' };
const emptyMessage: Partial<GcrFltMessage> = createEmptyMessageWithDate();

export const useGcrFltStore = createFormStore<GcrFltLine, GcrFltMessage>({
  emptyLine,
  emptyMessage,
});
