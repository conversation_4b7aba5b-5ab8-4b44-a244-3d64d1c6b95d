import type { BaseFormStore } from '@src/typesGlobal';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { GcrFltLine, GcrFltLineFormInput, GcrFltMessage } from './types';

const emptyLine: GcrFltLineFormInput = { action: 'N' };

const today = new Date();
const month = today.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
const day = today.getDate().toString().padStart(2, '0');
const todayFormatted = `${day}${month}`;
const emptyMessage: Partial<GcrFltMessage> = { date: todayFormatted };

type GcrFltState = BaseFormStore<GcrFltLine, GcrFltMessage>;

export const useGcrFltStore = create<GcrFltState>()(
  immer((set) => ({
    // Message:
    message: null,
    setMessage: (message) =>
      set((state) => {
        state.message = message;
      }),
    clearMessage: () =>
      set((state) => {
        state.message = null;
      }),
    // Message form:
    formMessage: emptyMessage,
    setFormMessagePartial: (updates) =>
      set((state) => {
        Object.assign(state.formMessage || {}, updates);
      }),
    clearFormMessage: () =>
      set((state) => {
        state.formMessage = emptyMessage;
      }),
    // Form message error map:
    formMessageErrors: null,
    setFormMessageErrors: (errors) =>
      set((state) => {
        state.formMessageErrors = errors;
      }),
    clearFormMessageErrors: () =>
      set((state) => {
        state.formMessageErrors = null;
      }),
    // Lines:
    lines: [],
    addLine: (line) =>
      set((state) => {
        state.lines.push(line);
      }),
    updateLine: (updates) =>
      set((state) => {
        if (state.formLineIndex === null) return;
        Object.assign(state.lines[state.formLineIndex], updates);
      }),
    deleteLine: (index) =>
      set((state) => {
        state.lines.splice(index, 1);
      }),
    clearAllLines: () =>
      set((state) => {
        state.lines = [];
      }),
    // Edit line:
    formLine: emptyLine,
    formLineIndex: null,
    setFormLine: (index = null) =>
      set((state) => {
        state.formLineIndex = index;
        if (index === null) return;
        state.formLine = state.lines[index || 0];
      }),
    setFormLinePartial: (updates) =>
      set((state) => {
        Object.assign(state.formLine || {}, updates);
      }),
    clearFormLine: () =>
      set((state) => {
        state.formLine = emptyLine;
        state.formLineIndex = null;
        state.formLineErrors = null;
      }),
    // Line error state:
    formLineErrors: null,
    setFormLineErrors: (errors) =>
      set((state) => {
        state.formLineErrors = errors;
      }),
    clearFormLineErrors: () =>
      set((state) => {
        state.formLineErrors = null;
      }),
  })),
);
