import { createFormStore, createEmptyMessageWithDate } from '@src/utils/createFormStore';
import type { GcrFltLine, GcrFltLineFormInput, GcrFltMessage } from './types';

const emptyLine: GcrFltLineFormInput = { action: 'N' };
const emptyMessage: Partial<GcrFltMessage> = createEmptyMessageWithDate();

export const useGcrFltStore = createFormStore<GcrFltLine, GcrFltMessage>({
  emptyLine,
  emptyMessage,
});
