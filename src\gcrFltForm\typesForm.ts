import type { GcrFltLine, GcrFltMessage } from './types';

export type GcrFltFormLineInput = {
  storeKey: keyof GcrFltLine;
  label: string;
  type: 'text' | 'number' | 'select';
  options?: readonly string[];
  toUpperCase?: boolean;
};

export type GcrFltFormLineConfig = {
  inputs: {
    [K in keyof GcrFltLine]-?: GcrFltFormLineInput;
  };
};

export type GcrFltFormLineMethods = {
  handleChange: (key: keyof GcrFltLine, value: GcrFltLine[keyof GcrFltLine]) => void;
  handleAddLine: () => void;
};

export type GcrFltFormMsgInput = {
  storeKey: keyof GcrFltMessage;
  label: string;
  type: 'text';
  toUpperCase?: boolean;
};

export type GcrFltFormMsgConfig = {
  inputs: {
    [K in keyof GcrFltMessage]-?: GcrFltFormMsgInput;
  };
};

export type GcrFltFormMsgMethods = {
  handleChange: (key: keyof GcrFltMessage, value: GcrFltMessage[keyof GcrFltMessage]) => void;
  handleAddMessage: () => void;
};
